<!DOCTYPE html>
<html>
<head>
    <title>Clean Test - No Conflicts</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-result { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
        button { padding: 10px 20px; margin: 5px; background: #007cba; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #005a87; }
        #chatTest { border: 1px solid #ccc; padding: 20px; margin: 20px 0; border-radius: 5px; }
    </style>
</head>
<body>
    <h1>LexAI Clean Test - No Conflicts</h1>
    
    <div id="results"></div>
    
    <div id="chatTest">
        <h3>Chat Test</h3>
        <input type="text" id="testMessage" placeholder="Escribe un mensaje de prueba..." style="width: 70%; padding: 10px;">
        <button onclick="sendTestMessage()">Enviar</button>
        <div id="chatResults" style="margin-top: 20px;"></div>
    </div>
    
    <script>
        // Test environment
        function runTests() {
            const results = document.getElementById('results');
            results.innerHTML = '<h2>Running Tests...</h2>';
            
            // Test 1: jQuery
            try {
                if (typeof $ !== 'undefined') {
                    addResult('✅ jQuery loaded successfully', 'success');
                } else {
                    addResult('❌ jQuery not found', 'error');
                }
            } catch (e) {
                addResult('❌ jQuery error: ' + e.message, 'error');
            }
            
            // Test 2: No conflicts
            try {
                if (typeof window.lazyloadRunObserver === 'undefined') {
                    addResult('✅ No lazyloadRunObserver conflicts', 'success');
                } else {
                    addResult('⚠️ lazyloadRunObserver exists - potential conflict', 'warning');
                }
            } catch (e) {
                addResult('❌ Conflict check error: ' + e.message, 'error');
            }
            
            // Test 3: AJAX capability
            try {
                $.ajax({
                    url: '/wp-admin/admin-ajax.php',
                    type: 'POST',
                    data: { action: 'heartbeat', test: 'ping' },
                    timeout: 5000,
                    success: function(response) {
                        addResult('✅ AJAX connection working', 'success');
                    },
                    error: function(xhr, status, error) {
                        if (xhr.status === 400 || xhr.status === 403) {
                            addResult('⚠️ AJAX endpoint reachable but authentication required', 'warning');
                        } else {
                            addResult('❌ AJAX connection failed: ' + error, 'error');
                        }
                    }
                });
            } catch (e) {
                addResult('❌ AJAX test error: ' + e.message, 'error');
            }
            
            // Test 4: SSL/Security
            if (location.protocol === 'https:') {
                addResult('✅ HTTPS connection secure', 'success');
            } else {
                addResult('⚠️ HTTP connection - may cause security issues', 'warning');
            }
        }
        
        function addResult(message, type) {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = 'test-result ' + type;
            div.innerHTML = message;
            results.appendChild(div);
        }
        
        function sendTestMessage() {
            const message = document.getElementById('testMessage').value;
            if (!message.trim()) {
                alert('Por favor escribe un mensaje');
                return;
            }
            
            const resultsDiv = document.getElementById('chatResults');
            resultsDiv.innerHTML = '<p>Enviando mensaje: "' + message + '"...</p>';
            
            // Test the actual LexAI endpoint
            $.ajax({
                url: '/wp-admin/admin-ajax.php',
                type: 'POST',
                data: {
                    action: 'lexai_send_message_fullpage',
                    message: message,
                    nonce: 'test' // This will fail but we can see the error
                },
                timeout: 10000,
                success: function(response) {
                    resultsDiv.innerHTML = '<div class="success">✅ Success: ' + JSON.stringify(response) + '</div>';
                },
                error: function(xhr, status, error) {
                    let errorMsg = 'Status: ' + status + ', Error: ' + error;
                    if (xhr.responseText) {
                        try {
                            const parsed = JSON.parse(xhr.responseText);
                            errorMsg = 'Response: ' + JSON.stringify(parsed);
                        } catch (e) {
                            errorMsg += ', Response: ' + xhr.responseText.substring(0, 200);
                        }
                    }
                    resultsDiv.innerHTML = '<div class="error">❌ Error: ' + errorMsg + '</div>';
                }
            });
        }
        
        // Run tests on page load
        $(document).ready(function() {
            runTests();
        });
    </script>
</body>
</html>
