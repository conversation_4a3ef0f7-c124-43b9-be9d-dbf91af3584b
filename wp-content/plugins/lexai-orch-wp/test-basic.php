<?php
/**
 * Ultra Basic Test - No WordPress
 */

echo "<h1>Ultra Basic Test</h1>";
echo "<p>PHP Version: " . PHP_VERSION . "</p>";
echo "<p>Memory Limit: " . ini_get('memory_limit') . "</p>";
echo "<p>Max Execution Time: " . ini_get('max_execution_time') . "</p>";
echo "<p>Current Time: " . date('Y-m-d H:i:s') . "</p>";

// Test basic operations
$start = microtime(true);
for ($i = 0; $i < 1000; $i++) {
    $test = "test" . $i;
}
$time = microtime(true) - $start;
echo "<p>Basic PHP Loop: " . round($time * 1000, 2) . "ms</p>";

echo "<p>✅ Basic PHP is working fine</p>";
echo "<p>The problem is likely in WordPress or plugin initialization</p>";
?>
