<?php
/**
 * Database Management Class
 *
 * @package LexAI
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * LexAI Database Class
 */
class LexAI_DB {
    
    /**
     * Constructor
     */
    public function __construct() {
        add_action('lexai_daily_cleanup', array($this, 'daily_cleanup'));
        add_action('lexai_weekly_cleanup', array($this, 'weekly_cleanup'));
    }
    
    /**
     * Get conversation by ID
     */
    public function get_conversation($conversation_id, $user_id = null) {
        global $wpdb;
        
        $conversations_table = $wpdb->prefix . LEXAI_CONVERSATIONS_TABLE;
        
        $sql = "SELECT * FROM $conversations_table WHERE id = %d";
        $params = array($conversation_id);
        
        if ($user_id !== null) {
            $sql .= " AND user_id = %d";
            $params[] = $user_id;
        }
        
        return $wpdb->get_row($wpdb->prepare($sql, $params));
    }
    
    /**
     * Create new conversation
     */
    public function create_conversation($user_id, $title = null) {
        global $wpdb;
        
        $conversations_table = $wpdb->prefix . LEXAI_CONVERSATIONS_TABLE;
        
        $result = $wpdb->insert(
            $conversations_table,
            array(
                'user_id' => $user_id,
                'title' => $title ?: __('Nueva Conversación', 'lexai'),
                'status' => 'active'
            ),
            array('%d', '%s', '%s')
        );
        
        return $result ? $wpdb->insert_id : false;
    }
    
    /**
     * Get conversation messages (with user verification)
     */
    public function get_conversation_messages($conversation_id, $limit = 50, $user_id = null) {
        global $wpdb;

        $messages_table = $wpdb->prefix . LEXAI_MESSAGES_TABLE;
        $conversations_table = $wpdb->prefix . LEXAI_CONVERSATIONS_TABLE;

        if ($user_id !== null) {
            $sql = "SELECT m.* FROM $messages_table m
                    INNER JOIN $conversations_table c ON m.conversation_id = c.id
                    WHERE m.conversation_id = %d AND c.user_id = %d
                    ORDER BY m.created_at ASC
                    LIMIT %d";
            return $wpdb->get_results($wpdb->prepare($sql, $conversation_id, $user_id, $limit));
        }

        return $wpdb->get_results($wpdb->prepare(
            "SELECT * FROM $messages_table
             WHERE conversation_id = %d
             ORDER BY created_at ASC
             LIMIT %d",
            $conversation_id,
            $limit
        ));
    }

    /**
     * Get conversations with message counts (OPTIMIZED - NO N+1)
     */
    public function get_conversations_with_stats($user_id, $limit = 20) {
        global $wpdb;

        $conversations_table = $wpdb->prefix . LEXAI_CONVERSATIONS_TABLE;
        $messages_table = $wpdb->prefix . LEXAI_MESSAGES_TABLE;

        $sql = "
            SELECT
                c.*,
                COUNT(m.id) as message_count,
                MAX(m.created_at) as last_message_at,
                (SELECT content FROM $messages_table m2
                 WHERE m2.conversation_id = c.id
                 ORDER BY m2.created_at DESC LIMIT 1) as last_message_preview
            FROM $conversations_table c
            LEFT JOIN $messages_table m ON c.id = m.conversation_id
            WHERE c.user_id = %d AND c.status != 'deleted'
            GROUP BY c.id
            ORDER BY COALESCE(MAX(m.created_at), c.created_at) DESC
            LIMIT %d
        ";

        return $wpdb->get_results($wpdb->prepare($sql, $user_id, $limit));
    }

    /**
     * Get agents with usage statistics (OPTIMIZED - NO N+1)
     */
    public function get_agents_with_usage_stats($limit = 50) {
        global $wpdb;

        $agents_table = $wpdb->prefix . LEXAI_AGENTS_TABLE;
        $tasks_table = $wpdb->prefix . LEXAI_TASKS_TABLE;
        $task_executions_table = $wpdb->prefix . LEXAI_TASK_EXECUTIONS_TABLE;

        $sql = "
            SELECT
                a.*,
                COUNT(DISTINCT t.id) as total_tasks,
                COUNT(DISTINCT CASE WHEN t.status = 'completed' THEN t.id END) as completed_tasks,
                COUNT(DISTINCT CASE WHEN t.status = 'failed' THEN t.id END) as failed_tasks,
                AVG(te.execution_time_ms) as avg_execution_time,
                MAX(t.created_at) as last_used_at
            FROM $agents_table a
            LEFT JOIN $tasks_table t ON a.id = t.agent_id
            LEFT JOIN $task_executions_table te ON t.id = te.task_id
            WHERE a.status = 'active'
            GROUP BY a.id
            ORDER BY total_tasks DESC, a.name ASC
            LIMIT %d
        ";

        return $wpdb->get_results($wpdb->prepare($sql, $limit));
    }

    /**
     * Batch insert messages (OPTIMIZED)
     */
    public function batch_insert_messages($messages_data) {
        global $wpdb;

        if (empty($messages_data)) {
            return false;
        }

        $messages_table = $wpdb->prefix . LEXAI_MESSAGES_TABLE;

        $values = array();
        $placeholders = array();

        foreach ($messages_data as $message) {
            $values[] = $message['conversation_id'];
            $values[] = $message['role'];
            $values[] = $message['content'];
            $values[] = isset($message['metadata']) ? json_encode($message['metadata']) : null;
            $values[] = current_time('mysql');

            $placeholders[] = '(%d, %s, %s, %s, %s)';
        }

        $sql = "INSERT INTO $messages_table (conversation_id, role, content, metadata, created_at) VALUES "
               . implode(', ', $placeholders);

        return $wpdb->query($wpdb->prepare($sql, $values));
    }
    
    /**
     * Add message to conversation
     */
    public function add_message($conversation_id, $role, $content, $metadata = null) {
        global $wpdb;
        
        $messages_table = $wpdb->prefix . LEXAI_MESSAGES_TABLE;
        
        $result = $wpdb->insert(
            $messages_table,
            array(
                'conversation_id' => $conversation_id,
                'role' => $role,
                'content' => $content,
                'metadata' => $metadata ? json_encode($metadata) : null
            ),
            array('%d', '%s', '%s', '%s')
        );
        
        if ($result) {
            $conversations_table = $wpdb->prefix . LEXAI_CONVERSATIONS_TABLE;
            $wpdb->update(
                $conversations_table,
                array('updated_at' => current_time('mysql')),
                array('id' => $conversation_id),
                array('%s'),
                array('%d')
            );
        }
        
        return $result ? $wpdb->insert_id : false;
    }
    
    /**
     * Get user conversations
     */
    public function get_user_conversations($user_id, $limit = 20, $offset = 0) {
        global $wpdb;
        
        $conversations_table = $wpdb->prefix . LEXAI_CONVERSATIONS_TABLE;
        
        return $wpdb->get_results($wpdb->prepare(
            "SELECT * FROM $conversations_table 
             WHERE user_id = %d AND status != 'deleted'
             ORDER BY updated_at DESC 
             LIMIT %d OFFSET %d",
            $user_id,
            $limit,
            $offset
        ));
    }
    
    /**
     * Get all agents
     */
    public function get_agents($status = 'active') {
        global $wpdb;

        $agents_table = $wpdb->prefix . LEXAI_AGENTS_TABLE;

        $sql = "SELECT * FROM $agents_table";
        $params = array();

        if ($status) {
            $sql .= " WHERE status = %s";
            $params[] = $status;
        }

        $sql .= " ORDER BY name ASC";

        if (empty($params)) {
            return $wpdb->get_results($sql);
        }

        return $wpdb->get_results($wpdb->prepare($sql, $params));
    }
    
    /**
     * Get agent by ID
     */
    public function get_agent($agent_id) {
        global $wpdb;
        
        $agents_table = $wpdb->prefix . LEXAI_AGENTS_TABLE;
        
        return $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $agents_table WHERE id = %d",
            $agent_id
        ));
    }
    
    /**
     * Create or update agent
     */
    public function save_agent($agent_data, $agent_id = null) {
        global $wpdb;

        $agents_table = $wpdb->prefix . LEXAI_AGENTS_TABLE;

        $data = array(
            'name' => $agent_data['name'],
            'description' => $agent_data['description'],
            'system_instruction' => $agent_data['system_instruction'],
            'tools' => json_encode($agent_data['tools']),
            'model' => $agent_data['model'] ?? 'gemini-2.5-flash',
            'max_output_tokens' => $agent_data['max_output_tokens'] ?? 8192,
            'status' => $agent_data['status'] ?? 'active'
        );

        $formats = array('%s', '%s', '%s', '%s', '%s', '%d', '%s');

        if ($agent_id) {
            $result = $wpdb->update($agents_table, $data, array('id' => $agent_id), $formats, array('%d'));
            return $result !== false ? $agent_id : false;
        } else {
            $data['created_by'] = get_current_user_id();
            $formats[] = '%d';

            $result = $wpdb->insert($agents_table, $data, $formats);
            return $result ? $wpdb->insert_id : false;
        }
    }
    
    /**
     * Delete agent
     */
    public function delete_agent($agent_id) {
        global $wpdb;
        
        $agents_table = $wpdb->prefix . LEXAI_AGENTS_TABLE;
        
        return $wpdb->delete($agents_table, array('id' => $agent_id), array('%d'));
    }
    
    /**
     * Log usage
     */
    public function log_usage($user_id, $conversation_id, $action_type, $tokens_used = 0, $api_key_id = null) {
        global $wpdb;
        
        $usage_logs_table = $wpdb->prefix . LEXAI_USAGE_LOGS_TABLE;
        
        return $wpdb->insert(
            $usage_logs_table,
            array(
                'user_id' => $user_id,
                'conversation_id' => $conversation_id,
                'action_type' => $action_type,
                'tokens_used' => $tokens_used,
                'api_key_id' => $api_key_id
            ),
            array('%d', '%d', '%s', '%d', '%d')
        );
    }
    
    /**
     * Get user usage stats
     */
    public function get_user_usage_stats($user_id, $period = 'daily') {
        global $wpdb;
        
        $usage_logs_table = $wpdb->prefix . LEXAI_USAGE_LOGS_TABLE;
        
        $date_condition = '';
        switch ($period) {
            case 'daily':
                $date_condition = "DATE(created_at) = CURDATE()";
                break;
            case 'monthly':
                $date_condition = "YEAR(created_at) = YEAR(CURDATE()) AND MONTH(created_at) = MONTH(CURDATE())";
                break;
            case 'weekly':
                $date_condition = "YEARWEEK(created_at) = YEARWEEK(CURDATE())";
                break;
        }
        
        return $wpdb->get_row($wpdb->prepare(
            "SELECT 
                COUNT(*) as total_actions,
                COUNT(CASE WHEN action_type = 'message' THEN 1 END) as messages_count,
                SUM(tokens_used) as total_tokens
             FROM $usage_logs_table 
             WHERE user_id = %d AND $date_condition",
            $user_id
        ));
    }
    
    /**
     * Daily cleanup routine
     */
    public function daily_cleanup() {
        $this->cleanup_old_sessions();
        $this->cleanup_failed_tasks();
    }
    
    /**
     * Weekly cleanup routine
     */
    public function weekly_cleanup() {
        $this->cleanup_old_conversations();
        $this->cleanup_old_usage_logs();
    }
    
    /**
     * Cleanup old sessions
     */
    private function cleanup_old_sessions() {
        global $wpdb;

        // Get all session transients and check their expiration properly
        $session_transients = $wpdb->get_results(
            "SELECT option_name FROM {$wpdb->options}
             WHERE option_name LIKE '_transient_lexai_session_%'"
        );

        $expired_time = time() - DAY_IN_SECONDS;

        foreach ($session_transients as $transient) {
            $session_key = str_replace('_transient_', '', $transient->option_name);
            $session_data = get_transient($session_key);

            // If transient doesn't exist or is expired, or if last_activity is old
            if ($session_data === false ||
                (isset($session_data['last_activity']) && $session_data['last_activity'] < $expired_time)) {
                delete_transient($session_key);
            }
        }
    }
    
    /**
     * Cleanup failed tasks
     */
    private function cleanup_failed_tasks() {
        global $wpdb;
        
        $tasks_table = $wpdb->prefix . LEXAI_TASKS_TABLE;
        
        $wpdb->query($wpdb->prepare(
            "UPDATE $tasks_table 
             SET status = 'failed' 
             WHERE status = 'in_progress' 
             AND started_at < %s",
            date('Y-m-d H:i:s', time() - HOUR_IN_SECONDS)
        ));
    }
    
    /**
     * Cleanup old conversations
     */
    private function cleanup_old_conversations() {
        global $wpdb;
        
        $conversations_table = $wpdb->prefix . LEXAI_CONVERSATIONS_TABLE;
        
        $wpdb->query($wpdb->prepare(
            "DELETE FROM $conversations_table 
             WHERE status = 'deleted' 
             AND updated_at < %s",
            date('Y-m-d H:i:s', time() - (30 * DAY_IN_SECONDS))
        ));
    }
    
    /**
     * Cleanup old usage logs
     */
    private function cleanup_old_usage_logs() {
        global $wpdb;
        
        $usage_logs_table = $wpdb->prefix . LEXAI_USAGE_LOGS_TABLE;
        
        $wpdb->query($wpdb->prepare(
            "DELETE FROM $usage_logs_table 
             WHERE created_at < %s",
            date('Y-m-d H:i:s', time() - (90 * DAY_IN_SECONDS))
        ));
    }

    /**
     * Save conversation (create or update)
     */
    public function save_conversation($conversation_data, $conversation_id = null) {
        global $wpdb;
        
        $conversations_table = $wpdb->prefix . LEXAI_CONVERSATIONS_TABLE;
        
        $data = array(
            'user_id' => $conversation_data['user_id'],
            'title' => $conversation_data['title'],
            'status' => $conversation_data['status'] ?? 'active'
        );
        
        if ($conversation_id) {
            $data['updated_at'] = current_time('mysql');
            $result = $wpdb->update($conversations_table, $data, array('id' => $conversation_id));
            return $result !== false ? $conversation_id : false;
        } else {
            $result = $wpdb->insert($conversations_table, $data);
            return $result ? $wpdb->insert_id : false;
        }
    }

    /**
     * Save message
     */
    public function save_message($message_data) {
        global $wpdb;
        
        $messages_table = $wpdb->prefix . LEXAI_MESSAGES_TABLE;
        
        $data = array(
            'conversation_id' => $message_data['conversation_id'],
            'role' => $message_data['role'],
            'content' => $message_data['content'],
            'files' => $message_data['files'] ?? null,
            'metadata' => $message_data['metadata'] ?? null,
            'agent_id' => $message_data['agent_id'] ?? null,
            'user_id' => $message_data['user_id'] ?? null
        );
        
        $result = $wpdb->insert($messages_table, $data);
        
        if ($result) {
            // Update conversation timestamp
            $conversations_table = $wpdb->prefix . LEXAI_CONVERSATIONS_TABLE;
            $wpdb->update(
                $conversations_table,
                array('updated_at' => current_time('mysql')),
                array('id' => $message_data['conversation_id'])
            );
        }
        
        return $result ? $wpdb->insert_id : false;
    }

    /**
     * Get message by ID
     */
    public function get_message($message_id) {
        global $wpdb;
        
        $messages_table = $wpdb->prefix . LEXAI_MESSAGES_TABLE;
        
        return $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $messages_table WHERE id = %d",
            $message_id
        ));
    }

    /**
     * Update message metadata
     */
    public function update_message_metadata($message_id, $metadata) {
        global $wpdb;
        
        $messages_table = $wpdb->prefix . LEXAI_MESSAGES_TABLE;
        
        return $wpdb->update(
            $messages_table,
            array('metadata' => json_encode($metadata)),
            array('id' => $message_id)
        );
    }
}