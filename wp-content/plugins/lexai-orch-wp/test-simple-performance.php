<?php
/**
 * Simple Performance Test
 */

// Start timing
$start_time = microtime(true);

echo "<h1>Simple Performance Test</h1>";
echo "<p>Starting test at: " . date('Y-m-d H:i:s') . "</p>";

// Test 1: Basic PHP
$php_start = microtime(true);
for ($i = 0; $i < 1000; $i++) {
    $test = "test" . $i;
}
$php_time = microtime(true) - $php_start;
echo "<p>✅ PHP Basic Operations: " . round($php_time * 1000, 2) . "ms</p>";

// Test 2: WordPress Load
$wp_start = microtime(true);
try {
    require_once('../../../wp-config.php');
    $wp_time = microtime(true) - $wp_start;
    echo "<p>✅ WordPress Load: " . round($wp_time * 1000, 2) . "ms</p>";
} catch (Exception $e) {
    echo "<p>❌ WordPress Load Failed: " . $e->getMessage() . "</p>";
    exit;
}

// Test 3: Basic WordPress Functions
$wp_func_start = microtime(true);
$user_id = get_current_user_id();
$site_url = get_site_url();
$wp_func_time = microtime(true) - $wp_func_start;
echo "<p>✅ WordPress Functions: " . round($wp_func_time * 1000, 2) . "ms</p>";

// Test 4: Plugin Class Existence
$class_start = microtime(true);
$lexai_exists = class_exists('LexAI');
$class_time = microtime(true) - $class_start;
echo "<p>✅ LexAI Class Check: " . round($class_time * 1000, 2) . "ms (Exists: " . ($lexai_exists ? 'Yes' : 'No') . ")</p>";

// Test 5: Memory Usage
$memory = memory_get_usage(true);
$memory_peak = memory_get_peak_usage(true);
echo "<p>✅ Memory Usage: " . round($memory / 1024 / 1024, 2) . "MB (Peak: " . round($memory_peak / 1024 / 1024, 2) . "MB)</p>";

// Only proceed with plugin tests if class exists
if ($lexai_exists) {
    // Test 6: Plugin Instance (CAREFUL)
    $plugin_start = microtime(true);
    try {
        $lexai = LexAI::get_instance();
        $plugin_time = microtime(true) - $plugin_start;
        echo "<p>✅ Plugin Instance: " . round($plugin_time * 1000, 2) . "ms</p>";
        
        // Test 7: Database Connection (CAREFUL)
        if (isset($lexai->db)) {
            $db_start = microtime(true);
            global $wpdb;
            $test_query = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->posts} LIMIT 1");
            $db_time = microtime(true) - $db_start;
            echo "<p>✅ Database Test: " . round($db_time * 1000, 2) . "ms</p>";
        } else {
            echo "<p>⚠️ Database object not initialized</p>";
        }
        
    } catch (Exception $e) {
        $plugin_time = microtime(true) - $plugin_start;
        echo "<p>❌ Plugin Instance Failed (" . round($plugin_time * 1000, 2) . "ms): " . $e->getMessage() . "</p>";
    }
} else {
    echo "<p>⚠️ LexAI plugin not loaded - skipping plugin tests</p>";
}

// Test 8: File System
$file_start = microtime(true);
$plugin_dir = WP_PLUGIN_DIR . '/lexai-orch-wp/';
$css_exists = file_exists($plugin_dir . 'assets/css/lexai-fullpage-chat.css');
$js_exists = file_exists($plugin_dir . 'assets/js/lexai-fullpage-chat.js');
$file_time = microtime(true) - $file_start;
echo "<p>✅ File System Check: " . round($file_time * 1000, 2) . "ms (CSS: " . ($css_exists ? 'Yes' : 'No') . ", JS: " . ($js_exists ? 'Yes' : 'No') . ")</p>";

// Total time
$total_time = microtime(true) - $start_time;
echo "<h2>Total Time: " . round($total_time * 1000, 2) . "ms</h2>";

// Performance Analysis
echo "<h2>Performance Analysis:</h2>";
if ($total_time > 1.0) {
    echo "<p style='color: red;'>⚠️ SLOW: Total time > 1 second</p>";
} elseif ($total_time > 0.5) {
    echo "<p style='color: orange;'>⚠️ MODERATE: Total time > 500ms</p>";
} else {
    echo "<p style='color: green;'>✅ FAST: Total time < 500ms</p>";
}

// Recommendations
echo "<h3>Quick Fixes:</h3>";
echo "<ul>";
echo "<li>Clear any object caches: <code>wp cache flush</code></li>";
echo "<li>Check for plugin conflicts</li>";
echo "<li>Verify database performance</li>";
echo "<li>Check server resources (CPU, Memory, Disk)</li>";
echo "</ul>";

echo "<p>Test completed at: " . date('Y-m-d H:i:s') . "</p>";
?>
