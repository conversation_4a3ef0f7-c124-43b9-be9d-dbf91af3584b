# Diagnóstico del Problema de Feedback en Tiempo Real en la Interfaz de Usuario

## 1. Resumen del Problema

La interfaz de usuario (frontend) no muestra ninguna información sobre el proceso de orquestación que ocurre en el backend. El usuario envía un mensaje y, tras una larga espera, recibe la respuesta final. No se visualiza la planificación de tareas generada por el orquestador, el estado de ejecución de cada agente, ni ninguna indicación de que el sistema está trabajando. Esto resulta en una mala experiencia de usuario (UX), ya que el usuario no tiene visibilidad del progreso.

## 2. Análisis de la Arquitectura Actual

Tras analizar los archivos clave (`lexai-fullpage.js`, `class-lexai-ajax.php`, `class-lexai-orchestrator.php` y `fullpage-chat-template.php`), se ha identificado que el problema es de naturaleza arquitectónica. El sistema sigue un **modelo de comunicación síncrono**, que es inadecuado para un proceso de larga duración con múltiples pasos.

El flujo actual es el siguiente:

1.  **Frontend (`lexai-fullpage.js`):** El usuario envía un mensaje. Se realiza una **única petición AJAX** al backend (`action: lexai_send_message`).
2.  **Backend (`class-lexai-ajax.php`):** La función `lexai_send_message_callback` recibe la petición.
3.  **Ejecución Síncrona:** Esta función invoca al `LexAI_Orchestrator` y espera a que el método `run()` **complete todo el proceso de orquestación de principio a fin**. Esto incluye la creación del plan y la ejecución de todas las tareas de los agentes.
4.  **Respuesta Final:** Solo cuando el orquestador ha terminado y ha generado la respuesta final, la función AJAX devuelve esta respuesta al frontend.
5.  **Frontend:** El JavaScript recibe la respuesta final y la muestra en el chat.

Este diseño hace **imposible** mostrar actualizaciones en tiempo real.

## 3. Puntos Críticos del Fallo

### 3.1. Backend: Procesamiento Bloqueante y Síncrono

-   **`class-lexai-ajax.php`:** La llamada a `$orchestrator->run()` es bloqueante. El script de PHP espera hasta que todo el trabajo esté hecho. No hay forma de que el frontend pueda consultar el estado mientras el proceso está en marcha.
-   **`class-lexai-orchestrator.php`:** El estado del orquestador (el plan, la tarea actual, los resultados intermedios) se almacena en variables locales dentro del método `run()`. Una vez que el método termina, este estado se pierde. **No hay persistencia del estado de la orquestación** (por ejemplo, en la base de datos o en transitorios de WordPress), lo que impide que un proceso externo pueda consultarlo.

### 3.2. Frontend: Ausencia de un Mecanismo de Sondeo (Polling)

-   **`public/js/lexai-fullpage.js`:** El código JavaScript no implementa un mecanismo de sondeo (polling). Realiza una única petición y espera una única respuesta. Para mostrar actualizaciones, el frontend necesitaría:
    1.  Enviar la petición inicial.
    2.  Recibir una respuesta inmediata del backend con un `ID de sesión` o `ID de proceso`.
    3.  Usar este ID para realizar peticiones AJAX periódicas (`setInterval`) a un nuevo endpoint del backend para preguntar por el estado del proceso.
    4.  Manejar las diferentes respuestas de estado (ej. "planificando", "ejecutando tarea X", "finalizado") y actualizar la interfaz de usuario dinámicamente.

### 3.3. Interfaz de Usuario (UI): Carencia de Componentes Visuales

-   **`templates/fullpage-chat-template.php`:** El HTML de la plantilla de chat es muy básico. Carece de elementos contenedores específicos para mostrar una lista de tareas del plan, el estado de cada tarea (pendiente, en proceso, completado) o animaciones de carga para cada paso. Estos elementos tendrían que ser creados y manipulados dinámicamente por el JavaScript.

## 4. Conclusión del Diagnóstico

El problema no es un simple bug, sino una **deficiencia fundamental en la arquitectura de comunicación entre el frontend y el backend**. El sistema está diseñado como un proceso monolítico y síncrono, cuando por su naturaleza (un orquestador con múltiples pasos) requiere una **arquitectura asíncrona basada en sondeo (polling) o WebSockets**.

Para solucionarlo, es necesario un rediseño significativo del flujo de interacción:

1.  **Modificar el Backend:**
    -   La petición AJAX inicial debe iniciar el proceso de orquestación en segundo plano (background) y devolver inmediatamente un `ID de proceso`.
    -   El orquestador debe guardar su estado (plan, tarea actual, etc.) en la base de datos (o transitorios) asociado a ese `ID de proceso` en cada paso clave.
    -   Se debe crear un nuevo endpoint AJAX (`action: get_lexai_process_status`) que, dado un `ID de proceso`, consulte la base de datos y devuelva el estado actual.

2.  **Modificar el Frontend:**
    -   Tras la petición inicial, debe almacenar el `ID de proceso` recibido.
    -   Debe iniciar un `setInterval` para llamar al nuevo endpoint `get_lexai_process_status` cada pocos segundos.
    -   Debe implementar la lógica para interpretar la respuesta de estado y actualizar la UI dinámicamente: mostrar el plan, marcar tareas como "en proceso" o "completadas", y finalmente mostrar la respuesta final y detener el sondeo.
    -   Se debe enriquecer la UI con los componentes necesarios para visualizar este flujo.
