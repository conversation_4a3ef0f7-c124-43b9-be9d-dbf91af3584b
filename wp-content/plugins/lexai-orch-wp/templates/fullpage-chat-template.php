<?php
/**
 * Full Page Chat Template - Modern Interface
 * Complete chat application similar to Claude.ai, ChatGPT, Perplexity
 * 
 * @package LexAI
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get current user
$user_id = get_current_user_id();
if (!$user_id) {
    wp_redirect(wp_login_url());
    exit;
}

// Initialize required classes (optimized)
$lexai = LexAI::get_instance();

// Initialize required classes (simplified)
$lexai = LexAI::get_instance();

// Ensure usage limiter is initialized
if (!$lexai->usage_limiter) {
    $lexai->usage_limiter = new LexAI_Usage_Limiter($lexai->db);
}

// Initialize full page handler if not already done
if (!$lexai->public_fullpage) {
    $export_handler = new LexAI_Export_Handler();
    $lexai->public_fullpage = new LexAI_Public_FullPage($lexai->usage_limiter, $lexai->db, $export_handler);
}

$config = $lexai->public_fullpage->get_chatbot_config($user_id);

// Get user info
$user_info = get_userdata($user_id);
$user_role = $lexai->usage_limiter->get_user_role($user_id);
$usage_stats = $lexai->usage_limiter->get_usage_stats($user_id);

// Enqueue FontAwesome if not already loaded
if (!wp_style_is('font-awesome', 'enqueued')) {
    wp_enqueue_style('font-awesome', 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css');
}

// Dequeue ALL conflicting scripts to prevent variable conflicts
wp_dequeue_script('lexai-fullpage-scripts');
wp_dequeue_script('lexai-advanced-js');
wp_dequeue_script('lexai-public-js');
wp_dequeue_script('lexai-auth-scripts');
wp_dequeue_script('lexai-auth');

// Also dequeue any styles that might conflict
wp_dequeue_style('lexai-fullpage-styles');
wp_dequeue_style('lexai-auth-styles');

// Enqueue LexAI Full Page Chat styles and scripts
wp_enqueue_style('lexai-fullpage-chat', LEXAI_PLUGIN_URL . 'assets/css/lexai-fullpage-chat.css', array(), LEXAI_VERSION);
wp_enqueue_script('lexai-fullpage-chat', LEXAI_PLUGIN_URL . 'assets/js/lexai-fullpage-chat.js', array('jquery'), LEXAI_VERSION, true);

// Localize script with configuration
wp_localize_script('lexai-fullpage-chat', 'lexaiConfig', array_merge($config, array(
    'pluginUrl' => LEXAI_PLUGIN_URL
)));
?>

<!DOCTYPE html>
<html <?php language_attributes(); ?>>
<head>
    <meta charset="<?php bloginfo('charset'); ?>">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php _e('LexAI - Asistente Legal Inteligente', 'lexai'); ?></title>
    <?php wp_head(); ?>
</head>
<body class="lexai-fullpage-chat" data-theme="light">

<!-- Animated Background -->
<div class="lexai-animated-bg">
    <div class="lexai-floating-element lexai-floating-1"></div>
    <div class="lexai-floating-element lexai-floating-2"></div>
    <div class="lexai-floating-element lexai-floating-3"></div>
    <div class="lexai-floating-element lexai-floating-4"></div>
    <div class="lexai-floating-element lexai-floating-5"></div>
</div>

<!-- Main Application Container -->
<div class="lexai-app">
    
    <!-- Sidebar -->
    <aside class="lexai-sidebar" id="lexai-sidebar" role="navigation" aria-label="<?php _e('Navegación de conversaciones', 'lexai'); ?>">
        <div class="lexai-sidebar-header">
            <div class="lexai-logo" role="img" aria-label="<?php _e('Logo de LexAI', 'lexai'); ?>">
                <i class="fas fa-balance-scale" aria-hidden="true"></i>
                <span>LexAI</span>
            </div>
            <button class="lexai-new-chat-btn" id="lexai-new-chat" aria-label="<?php _e('Crear nueva conversación', 'lexai'); ?>">
                <i class="fas fa-plus" aria-hidden="true"></i>
                <span><?php _e('Nueva conversación', 'lexai'); ?></span>
            </button>
        </div>

        <div class="lexai-sidebar-content">
            <!-- Search Conversations -->
            <div class="lexai-search-section">
                <div class="lexai-search-box">
                    <i class="fas fa-search" aria-hidden="true"></i>
                    <input type="text"
                           placeholder="<?php _e('Buscar conversaciones...', 'lexai'); ?>"
                           id="lexai-search-conversations"
                           aria-label="<?php _e('Buscar en conversaciones', 'lexai'); ?>"
                           role="searchbox">
                </div>
            </div>

            <!-- Conversations List -->
            <div class="lexai-conversations-section">
                <div class="lexai-conversations-list"
                     id="lexai-conversations-list"
                     role="list"
                     aria-label="<?php _e('Lista de conversaciones', 'lexai'); ?>">
                    <!-- Conversations will be loaded here -->
                </div>
            </div>
        </div>

        <div class="lexai-sidebar-footer">
            <!-- User Profile -->
            <div class="lexai-user-profile" id="lexai-user-profile">
                <div class="lexai-user-avatar">
                    <?php echo get_avatar($user_id, 32); ?>
                </div>
                <div class="lexai-user-info">
                    <div class="lexai-user-name"><?php echo esc_html($user_info->display_name); ?></div>
                    <div class="lexai-user-role"><?php echo esc_html(ucfirst($user_role)); ?></div>
                </div>
                <button class="lexai-user-menu-btn" id="lexai-user-menu">
                    <i class="fas fa-ellipsis-h"></i>
                </button>
            </div>
        </div>
    </aside>

    <!-- Main Content Area -->
    <main class="lexai-main">
        
        <!-- Header -->
        <header class="lexai-header">
            <div class="lexai-header-left">
                <button class="lexai-sidebar-toggle" id="lexai-sidebar-toggle">
                    <i class="fas fa-bars"></i>
                </button>
                <div class="lexai-conversation-title">
                    <h1 id="lexai-current-title"><?php _e('Nueva conversación', 'lexai'); ?></h1>
                </div>
            </div>
            
            <div class="lexai-header-right">
                <!-- Usage Stats -->
                <div class="lexai-usage-indicator" id="lexai-usage-indicator">
                    <span class="lexai-usage-text">
                        <?php printf(__('%d/%d consultas', 'lexai'), $usage_stats['used'], $usage_stats['limit']); ?>
                    </span>
                    <div class="lexai-usage-bar">
                        <div class="lexai-usage-fill" style="width: <?php echo ($usage_stats['limit'] > 0) ? ($usage_stats['used'] / $usage_stats['limit'] * 100) : 0; ?>%"></div>
                    </div>
                </div>

                <!-- Chat Actions -->
                <button class="lexai-btn-glass" id="lexai-export-chat" title="<?php _e('Exportar conversación', 'lexai'); ?>">
                    <i class="fas fa-file-export"></i>
                </button>
                
                <button class="lexai-btn-glass" id="lexai-clear-chat" title="<?php _e('Limpiar chat', 'lexai'); ?>">
                    <i class="fas fa-trash"></i>
                </button>

                <!-- Theme Toggle -->
                <button class="lexai-theme-toggle" id="lexai-theme-toggle" title="<?php _e('Cambiar tema', 'lexai'); ?>">
                    <i class="fas fa-sun lexai-theme-icon-light"></i>
                    <i class="fas fa-moon lexai-theme-icon-dark"></i>
                </button>

                <!-- Settings -->
                <button class="lexai-settings-btn" id="lexai-settings" title="<?php _e('Configuración', 'lexai'); ?>">
                    <i class="fas fa-cog"></i>
                </button>
            </div>
        </header>

        <!-- Chat Area -->
        <div class="lexai-chat-container">
            <div class="lexai-messages-area" id="lexai-messages-area" role="main" aria-label="<?php _e('Área de conversación', 'lexai'); ?>">
                <div class="lexai-messages-container"
                     id="lexai-messages-container"
                     role="log"
                     aria-live="polite"
                     aria-label="<?php _e('Mensajes de la conversación', 'lexai'); ?>">

                    <!-- Welcome Screen -->
                    <div class="lexai-welcome-screen" id="lexai-welcome-screen">
                        <div class="lexai-welcome-content">
                            <div class="lexai-welcome-logo" role="img" aria-label="<?php _e('Logo de LexAI', 'lexai'); ?>">
                                <i class="fas fa-balance-scale" aria-hidden="true"></i>
                            </div>
                            <h2><?php _e('¡Hola! Soy tu asistente legal especializado', 'lexai'); ?></h2>
                            <p><?php _e('Puedo ayudarte con consultas sobre derecho mexicano, análisis de documentos, redacción de contratos y más.', 'lexai'); ?></p>
                            
                            <!-- Quick Actions -->
                            <div class="lexai-quick-actions">
                                <button class="lexai-quick-action" data-query="¿Cuáles son los requisitos para constituir una sociedad anónima?">
                                    <i class="fas fa-building"></i>
                                    <span><?php _e('Sociedades', 'lexai'); ?></span>
                                </button>
                                <button class="lexai-quick-action" data-query="¿Cómo redactar un contrato de compraventa?">
                                    <i class="fas fa-file-contract"></i>
                                    <span><?php _e('Contratos', 'lexai'); ?></span>
                                </button>
                                <button class="lexai-quick-action" data-query="¿Cuáles son los derechos laborales básicos?">
                                    <i class="fas fa-users"></i>
                                    <span><?php _e('Laboral', 'lexai'); ?></span>
                                </button>
                                <button class="lexai-quick-action" data-query="¿Cuáles son los plazos para interponer un amparo?">
                                    <i class="fas fa-balance-scale"></i>
                                    <span><?php _e('Amparo', 'lexai'); ?></span>
                                </button>
                            </div>
                        </div>
                    </div>

                </div>

                <!-- Typing Indicator -->
                <div class="lexai-typing-indicator" id="lexai-typing-indicator" style="display: none;">
                    <div class="lexai-typing-avatar">
                        <i class="fas fa-balance-scale"></i>
                    </div>
                    <div class="lexai-typing-content">
                        <div class="lexai-typing-dots">
                            <span></span>
                            <span></span>
                            <span></span>
                        </div>
                        <span class="lexai-typing-text"><?php _e('LexAI está analizando...', 'lexai'); ?></span>
                    </div>
                </div>
            </div>

            <!-- Input Area -->
            <div class="lexai-input-area" role="region" aria-label="<?php _e('Área de entrada de mensajes', 'lexai'); ?>">
                <div class="lexai-input-container">
                    <div class="lexai-input-wrapper">
                        <button class="lexai-attach-btn"
                                id="lexai-attach-btn"
                                title="<?php _e('Adjuntar archivo', 'lexai'); ?>"
                                aria-label="<?php _e('Adjuntar archivo', 'lexai'); ?>">
                            <i class="fas fa-paperclip" aria-hidden="true"></i>
                        </button>

                        <textarea
                            id="lexai-message-input"
                            class="lexai-message-input"
                            placeholder="<?php _e('Escribe tu consulta legal aquí...', 'lexai'); ?>"
                            aria-label="<?php _e('Escribe tu consulta legal', 'lexai'); ?>"
                            rows="1"
                            maxlength="2000"
                            aria-describedby="lexai-char-count lexai-input-hint"
                        ></textarea>

                        <button class="lexai-voice-btn"
                                id="lexai-voice-btn"
                                title="<?php _e('Grabar mensaje de voz', 'lexai'); ?>"
                                aria-label="<?php _e('Grabar mensaje de voz', 'lexai'); ?>">
                            <i class="fas fa-microphone" aria-hidden="true"></i>
                        </button>

                        <button class="lexai-send-btn"
                                id="lexai-send-btn"
                                disabled
                                aria-label="<?php _e('Enviar mensaje', 'lexai'); ?>">
                            <i class="fas fa-paper-plane" aria-hidden="true"></i>
                        </button>
                    </div>
                    
                    <div class="lexai-input-footer"><div class="lexai-char-counter">
                            <span id="lexai-char-count">0</span>/2000
                        </div>
                        <span class="lexai-input-hint">
                            <?php _e('Presiona Enter para enviar, Shift+Enter para nueva línea', 'lexai'); ?>
                        </span>
                        <div class="lexai-powered-by">
                            <?php _e('Powered by', 'lexai'); ?> <strong>Gemini AI</strong>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>
</div>

<!-- Hidden file input -->
<input type="file" id="lexai-file-input" style="display: none;" accept=".pdf,.doc,.docx,.txt">

<!-- User Menu Dropdown -->
<div class="lexai-user-menu" id="lexai-user-menu-dropdown" style="display: none;">
    <div class="lexai-menu-item" id="lexai-profile-settings">
        <i class="fas fa-user"></i>
        <span><?php _e('Perfil', 'lexai'); ?></span>
    </div>
    <div class="lexai-menu-item" id="lexai-subscription-plans">
        <i class="fas fa-crown"></i>
        <span><?php _e('Planes', 'lexai'); ?></span>
    </div>
    <div class="lexai-menu-item" id="lexai-export-data">
        <i class="fas fa-download"></i>
        <span><?php _e('Exportar datos', 'lexai'); ?></span>
    </div>
    <div class="lexai-menu-divider"></div>
    <div class="lexai-menu-item" id="lexai-logout">
        <i class="fas fa-sign-out-alt"></i>
        <span><?php _e('Cerrar sesión', 'lexai'); ?></span>
    </div>
</div>

<!-- User Menu Overlay -->
<div class="lexai-user-menu-overlay" id="lexai-user-menu-overlay" style="display: none;"></div>

<!-- Subscription Plans Modal -->
<div class="lexai-modal" id="lexai-subscription-modal" style="display: none;">
    <div class="lexai-modal-content">
        <div class="lexai-modal-header">
            <h3 class="lexai-modal-title"><?php _e('Planes de Suscripción', 'lexai'); ?></h3>
            <button class="lexai-modal-close" id="lexai-subscription-modal-close">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="lexai-modal-body">
            <div class="lexai-subscription-plans">

                <!-- Basic Plan -->
                <div class="lexai-plan-card <?php echo ($user_role === 'basic') ? 'active' : ''; ?>">
                    <div class="lexai-plan-header">
                        <h4><?php _e('Básico', 'lexai'); ?></h4>
                        <div class="lexai-plan-price">
                            <span class="lexai-price">$0</span>
                            <span class="lexai-period"><?php _e('/mes', 'lexai'); ?></span>
                        </div>
                    </div>
                    <div class="lexai-plan-features">
                        <ul>
                            <li><i class="fas fa-check"></i> <?php _e('10 consultas por mes', 'lexai'); ?></li>
                            <li><i class="fas fa-check"></i> <?php _e('Consultas básicas de derecho', 'lexai'); ?></li>
                            <li><i class="fas fa-check"></i> <?php _e('Soporte por email', 'lexai'); ?></li>
                        </ul>
                    </div>
                    <?php if ($user_role !== 'basic'): ?>
                    <button class="lexai-btn lexai-btn-secondary lexai-plan-btn" data-plan="basic">
                        <?php _e('Cambiar a Básico', 'lexai'); ?>
                    </button>
                    <?php else: ?>
                    <button class="lexai-btn lexai-btn-primary lexai-plan-btn" disabled>
                        <?php _e('Plan Actual', 'lexai'); ?>
                    </button>
                    <?php endif; ?>
                </div>

                <!-- Premium Plan -->
                <div class="lexai-plan-card <?php echo ($user_role === 'premium') ? 'active' : ''; ?>">
                    <div class="lexai-plan-header">
                        <h4><?php _e('Premium', 'lexai'); ?></h4>
                        <div class="lexai-plan-price">
                            <span class="lexai-price">$29</span>
                            <span class="lexai-period"><?php _e('/mes', 'lexai'); ?></span>
                        </div>
                    </div>
                    <div class="lexai-plan-features">
                        <ul>
                            <li><i class="fas fa-check"></i> <?php _e('100 consultas por mes', 'lexai'); ?></li>
                            <li><i class="fas fa-check"></i> <?php _e('Análisis de documentos', 'lexai'); ?></li>
                            <li><i class="fas fa-check"></i> <?php _e('Redacción de contratos', 'lexai'); ?></li>
                            <li><i class="fas fa-check"></i> <?php _e('Soporte prioritario', 'lexai'); ?></li>
                        </ul>
                    </div>
                    <?php if ($user_role !== 'premium'): ?>
                    <button class="lexai-btn lexai-btn-primary lexai-plan-btn" data-plan="premium">
                        <?php _e('Actualizar a Premium', 'lexai'); ?>
                    </button>
                    <?php else: ?>
                    <button class="lexai-btn lexai-btn-primary lexai-plan-btn" disabled>
                        <?php _e('Plan Actual', 'lexai'); ?>
                    </button>
                    <?php endif; ?>
                </div>

                <!-- Enterprise Plan -->
                <div class="lexai-plan-card <?php echo ($user_role === 'enterprise') ? 'active' : ''; ?>">
                    <div class="lexai-plan-header">
                        <h4><?php _e('Empresarial', 'lexai'); ?></h4>
                        <div class="lexai-plan-price">
                            <span class="lexai-price">$99</span>
                            <span class="lexai-period"><?php _e('/mes', 'lexai'); ?></span>
                        </div>
                    </div>
                    <div class="lexai-plan-features">
                        <ul>
                            <li><i class="fas fa-check"></i> <?php _e('Consultas ilimitadas', 'lexai'); ?></li>
                            <li><i class="fas fa-check"></i> <?php _e('API personalizada', 'lexai'); ?></li>
                            <li><i class="fas fa-check"></i> <?php _e('Integración con sistemas', 'lexai'); ?></li>
                            <li><i class="fas fa-check"></i> <?php _e('Soporte 24/7', 'lexai'); ?></li>
                        </ul>
                    </div>
                    <?php if ($user_role !== 'enterprise'): ?>
                    <button class="lexai-btn lexai-btn-primary lexai-plan-btn" data-plan="enterprise">
                        <?php _e('Actualizar a Empresarial', 'lexai'); ?>
                    </button>
                    <?php else: ?>
                    <button class="lexai-btn lexai-btn-primary lexai-plan-btn" disabled>
                        <?php _e('Plan Actual', 'lexai'); ?>
                    </button>
                    <?php endif; ?>
                </div>

            </div>
        </div>
    </div>
</div>

<!-- Profile Settings Modal -->
<div class="lexai-modal" id="lexai-profile-modal" style="display: none;">
    <div class="lexai-modal-content">
        <div class="lexai-modal-header">
            <h3 class="lexai-modal-title"><?php _e('Configuración del Perfil', 'lexai'); ?></h3>
            <button class="lexai-modal-close" id="lexai-profile-modal-close">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="lexai-modal-body">
            <form id="lexai-profile-form">
                <div class="lexai-form-group">
                    <label for="lexai-display-name"><?php _e('Nombre de usuario', 'lexai'); ?></label>
                    <input type="text" id="lexai-display-name" value="<?php echo esc_attr($user_info->display_name); ?>" required>
                </div>

                <div class="lexai-form-group">
                    <label for="lexai-email"><?php _e('Correo electrónico', 'lexai'); ?></label>
                    <input type="email" id="lexai-email" value="<?php echo esc_attr($user_info->user_email); ?>" required>
                </div>

                <div class="lexai-form-group">
                    <label for="lexai-new-password"><?php _e('Nueva contraseña (opcional)', 'lexai'); ?></label>
                    <input type="password" id="lexai-new-password" placeholder="<?php _e('Dejar en blanco para mantener actual', 'lexai'); ?>">
                </div>

                <div class="lexai-form-group">
                    <label for="lexai-confirm-password"><?php _e('Confirmar contraseña', 'lexai'); ?></label>
                    <input type="password" id="lexai-confirm-password" placeholder="<?php _e('Confirmar nueva contraseña', 'lexai'); ?>">
                </div>
            </form>
        </div>
        <div class="lexai-modal-footer">
            <button class="lexai-btn lexai-btn-secondary" id="lexai-profile-cancel">
                <?php _e('Cancelar', 'lexai'); ?>
            </button>
            <button class="lexai-btn lexai-btn-primary" id="lexai-profile-save">
                <?php _e('Guardar cambios', 'lexai'); ?>
            </button>
        </div>
    </div>
</div>

<!-- Settings Modal -->
<div class="lexai-modal" id="lexai-settings-modal" style="display: none;">
    <div class="lexai-modal-content">
        <div class="lexai-modal-header">
            <h3 class="lexai-modal-title"><?php _e('Configuración', 'lexai'); ?></h3>
            <button class="lexai-modal-close" id="lexai-settings-modal-close">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="lexai-modal-body">
            <div class="lexai-settings-section">
                <h4><?php _e('Preferencias de Chat', 'lexai'); ?></h4>

                <div class="lexai-setting-item">
                    <label class="lexai-setting-label">
                        <input type="checkbox" id="lexai-auto-scroll" checked>
                        <span class="lexai-setting-text"><?php _e('Desplazamiento automático', 'lexai'); ?></span>
                    </label>
                </div>

                <div class="lexai-setting-item">
                    <label class="lexai-setting-label">
                        <input type="checkbox" id="lexai-sound-notifications">
                        <span class="lexai-setting-text"><?php _e('Notificaciones de sonido', 'lexai'); ?></span>
                    </label>
                </div>

                <div class="lexai-setting-item">
                    <label class="lexai-setting-label">
                        <input type="checkbox" id="lexai-save-conversations" checked>
                        <span class="lexai-setting-text"><?php _e('Guardar conversaciones', 'lexai'); ?></span>
                    </label>
                </div>
            </div>

            <div class="lexai-settings-section">
                <h4><?php _e('Privacidad', 'lexai'); ?></h4>

                <div class="lexai-setting-item">
                    <label class="lexai-setting-label">
                        <input type="checkbox" id="lexai-analytics" checked>
                        <span class="lexai-setting-text"><?php _e('Permitir análisis de uso', 'lexai'); ?></span>
                    </label>
                </div>

                <div class="lexai-setting-item">
                    <button class="lexai-btn lexai-btn-secondary" id="lexai-clear-data">
                        <i class="fas fa-trash"></i>
                        <?php _e('Eliminar todos los datos', 'lexai'); ?>
                    </button>
                </div>
            </div>
        </div>
        <div class="lexai-modal-footer">
            <button class="lexai-btn lexai-btn-secondary" id="lexai-settings-cancel">
                <?php _e('Cancelar', 'lexai'); ?>
            </button>
            <button class="lexai-btn lexai-btn-primary" id="lexai-settings-save">
                <?php _e('Guardar configuración', 'lexai'); ?>
            </button>
        </div>
    </div>
</div>

<!-- Export Modal -->
<div class="lexai-modal" id="lexai-export-modal" style="display: none;">
    <div class="lexai-modal-content">
        <div class="lexai-modal-header">
            <h3 class="lexai-modal-title"><?php _e('Exportar Conversación', 'lexai'); ?></h3>
            <button class="lexai-modal-close">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="lexai-modal-body">
            <div class="lexai-export-options">
                <button class="lexai-export-btn" data-format="pdf">
                    <span><i class="fas fa-file-pdf"></i></span>
                    <div>
                        <strong>PDF</strong>
                        <small><?php _e('Documento portable', 'lexai'); ?></small>
                    </div>
                </button>
                <button class="lexai-export-btn" data-format="docx">
                    <span><i class="fas fa-file-word"></i></span>
                    <div>
                        <strong>DOCX</strong>
                        <small><?php _e('Microsoft Word', 'lexai'); ?></small>
                    </div>
                </button>
                <button class="lexai-export-btn" data-format="md">
                    <span><i class="fab fa-markdown"></i></span>
                    <div>
                        <strong>Markdown</strong>
                        <small><?php _e('Texto plano', 'lexai'); ?></small>
                    </div>
                </button>
                <button class="lexai-export-btn" data-format="txt">
                    <span><i class="fas fa-file-alt"></i></span>
                    <div>
                        <strong>TXT</strong>
                        <small><?php _e('Texto simple', 'lexai'); ?></small>
                    </div>
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Voice Recording Modal -->
<div class="lexai-modal" id="lexai-voice-modal" style="display: none;">
    <div class="lexai-modal-content">
        <div class="lexai-modal-header">
            <h3 class="lexai-modal-title"><?php _e('Grabación de Voz', 'lexai'); ?></h3>
            <button class="lexai-modal-close">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="lexai-modal-body">
            <div class="lexai-voice-recorder">
                <div class="lexai-voice-visualizer">
                    <canvas id="lexai-voice-canvas" width="300" height="100"></canvas>
                </div>
                <div class="lexai-voice-controls">
                    <button class="lexai-voice-control-btn" id="lexai-start-recording">
                        <span><i class="fas fa-microphone"></i></span>
                        <?php _e('Iniciar', 'lexai'); ?>
                    </button>
                    <button class="lexai-voice-control-btn" id="lexai-stop-recording" style="display: none;">
                        <span><i class="fas fa-stop"></i></span>
                        <?php _e('Detener', 'lexai'); ?>
                    </button>
                    <button class="lexai-voice-control-btn" id="lexai-send-voice" style="display: none;">
                        <span><i class="fas fa-paper-plane"></i></span>
                        <?php _e('Enviar', 'lexai'); ?>
                    </button>
                </div>
                <div class="lexai-voice-status" id="lexai-voice-status">
                    <?php _e('Presiona iniciar para comenzar a grabar', 'lexai'); ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Document Modal -->
<div class="lexai-modal" id="lexai-document-modal" style="display: none;">
    <div class="lexai-modal-content">
        <div class="lexai-modal-header">
            <h3 class="lexai-modal-title"><?php _e('Contenido del Documento', 'lexai'); ?></h3>
            <button class="lexai-modal-close">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="lexai-modal-body">
            <div class="lexai-document-full-content"></div>
        </div>
        <div class="lexai-modal-footer">
            <button class="lexai-btn lexai-btn-secondary" id="lexai-copy-full-document">
                <i class="fas fa-clipboard"></i> <?php _e('Copiar Todo', 'lexai'); ?>
            </button>
        </div>
    </div>
</div>

<!-- File Preview Area -->
<div class="lexai-file-preview-area" id="lexai-file-preview-area" style="display: none;">
    <div class="lexai-file-previews" id="lexai-file-previews"></div>
</div>

<!-- Configuration Script -->
<!-- Toggle Button (for minimized mode) -->
<div class="lexai-toggle-container" id="lexai-toggle-container" style="display: none;">
    <button id="lexai-advanced-toggle" class="lexai-toggle-btn" title="<?php _e('Mostrar/Ocultar Chat', 'lexai'); ?>">
        <i class="fas fa-balance-scale"></i>
        <div class="lexai-notification-dot" id="lexai-notification-dot" style="display: none;"></div>
    </button>
</div>

<!-- Toast Notifications Container -->
<div class="lexai-toast-container" id="lexai-toast-container"></div>

<script type="application/json" id="lexai-fullpage-config">
<?php echo json_encode($config); ?>
</script>

<?php wp_footer(); ?>
</body>
</html>
