/**
 * LexAI Full Page Chat Interface Styles
 * Modern, responsive chat interface with glassmorphism effects
 * 
 * @package LexAI
 * @since 1.2.3
 */

/* ===== CSS VARIABLES ===== */
:root {
    /* Base Colors - Corporate Theme (Default) */
    --lexai-primary: #475569;
    --lexai-primary-hover: #64748b;
    --lexai-primary-light: rgba(71, 85, 105, 0.1);
    --lexai-accent: #64748b;
    --lexai-accent-hover: #94a3b8;
    
    /* Background Colors */
    --lexai-bg-main: #f8fafc;
    --lexai-bg-sidebar: rgba(51, 65, 85, 0.85);
    --lexai-bg-sidebar-collapsed: rgba(30, 41, 59, 0.85);
    --lexai-bg-card: rgba(248, 250, 252, 0.9);
    --lexai-bg-glass: rgba(248, 250, 252, 0.6);
    --lexai-bg-input: #ffffff;
    --lexai-bg-bubble: rgba(100, 116, 139, 0.06);
    
    /* Text Colors */
    --lexai-text-primary: #1f2937;
    --lexai-text-secondary: #6b7280;
    --lexai-text-tertiary: #ffffff;
    --lexai-text-sidebar: #ffffff;
    
    /* Border & Effects */
    --lexai-border: rgba(71, 85, 105, 0.1);
    --lexai-shadow: 0 6px 20px rgba(71, 85, 105, 0.15);
    --lexai-backdrop-blur: blur(12px);
    
    /* Gradients */
    --lexai-gradient-primary: linear-gradient(135deg, #64748b 0%, #475569 100%);
    --lexai-gradient-secondary: linear-gradient(135deg, #94a3b8 0%, #64748b 100%);
    
    /* Status Colors */
    --lexai-success: #10b981;
    --lexai-warning: #f59e0b;
    --lexai-error: #ef4444;
    --lexai-info: #3b82f6;

    /* Dimensions */
    --lexai-sidebar-width: 280px;
    --lexai-sidebar-collapsed-width: 70px;
    --lexai-header-height: 70px;
    --lexai-input-height: 80px;
    --lexai-border-radius: 12px;
    --lexai-border-radius-lg: 16px;
    
    /* Transitions */
    --lexai-transition: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --lexai-transition-fast: 0.15s ease;
    
    /* Z-Index Scale */
    --lexai-z-sidebar: 40;
    --lexai-z-header: 30;
    --lexai-z-overlay: 50;
    --lexai-z-modal: 100;
    --lexai-z-toast: 200;
}

/* Dark Theme Variables */
.lexai-theme-dark {
    /* Base Colors - Dark Theme - Más oscuros */
    --lexai-primary: #374151;
    --lexai-primary-hover: #1f2937;
    --lexai-primary-light: rgba(55, 65, 81, 0.15);
    --lexai-accent: #ffffff; /* Cambiado de rojo a blanco */
    --lexai-accent-hover: #e5e7eb; /* Gris claro para hover */

    /* Background Colors - Más oscuros */
    --lexai-bg-main: #0a0f1c; /* Mucho más oscuro */
    --lexai-bg-sidebar: rgba(15, 23, 42, 0.95); /* Más oscuro */
    --lexai-bg-sidebar-collapsed: rgba(10, 15, 28, 0.95); /* Más oscuro */
    --lexai-bg-card: rgba(17, 24, 39, 0.95); /* Más oscuro */
    --lexai-bg-glass: rgba(31, 41, 55, 0.8); /* Más oscuro */
    --lexai-bg-input: #1f2937; /* Más oscuro */
    --lexai-bg-bubble: rgba(55, 65, 81, 0.1);

    /* Text Colors - Más claros */
    --lexai-text-primary: #ffffff; /* Blanco puro */
    --lexai-text-secondary: #e5e7eb; /* Más claro */
    --lexai-text-tertiary: #ffffff;
    --lexai-text-sidebar: #ffffff; /* Blanco puro */

    /* Border & Effects */
    --lexai-border: rgba(255, 255, 255, 0.1);
    --lexai-shadow: 0 8px 30px rgba(0, 0, 0, 0.5); /* Sombra más intensa */
    --lexai-backdrop-blur: blur(16px);

    /* Gradients - Más oscuros */
    --lexai-gradient-primary: linear-gradient(135deg, #374151 0%, #1f2937 100%);
    --lexai-gradient-secondary: linear-gradient(135deg, #1f2937 0%, #0a0f1c 100%);

    /* Status Colors - Iconos rojos cambiados a blanco */
    --lexai-success: #10b981;
    --lexai-warning: #f59e0b;
    --lexai-error: #ffffff; /* Cambiado de rojo a blanco */
    --lexai-info: #60a5fa;
}

/* ===== GLOBAL STYLES ===== */
* {
    box-sizing: border-box;
}

body.lexai-fullpage-chat {
    margin: 0;
    padding: 0;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    background: var(--lexai-bg-main);
    color: var(--lexai-text-primary);
    overflow: hidden;
    height: 100vh;
    transition: background-color var(--lexai-transition), color var(--lexai-transition);
}

/* ===== ANIMATED BACKGROUND ===== */
.lexai-animated-bg {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    overflow: hidden;
    pointer-events: none;
}

.lexai-floating-element {
    position: absolute;
    border-radius: 50%;
    background: radial-gradient(circle at 60% 40%, var(--lexai-accent) 0%, var(--lexai-primary) 60%, transparent 100%);
    filter: blur(40px);
    opacity: 0.15;
    animation: lexai-float 20s ease-in-out infinite;
}

.lexai-floating-1 {
    width: 300px;
    height: 300px;
    top: 10%;
    left: 10%;
    animation-delay: 0s;
}

.lexai-floating-2 {
    width: 200px;
    height: 200px;
    top: 60%;
    right: 15%;
    animation-delay: -5s;
}

.lexai-floating-3 {
    width: 250px;
    height: 250px;
    bottom: 20%;
    left: 20%;
    animation-delay: -10s;
}

.lexai-floating-4 {
    width: 180px;
    height: 180px;
    top: 30%;
    right: 30%;
    animation-delay: -15s;
}

.lexai-floating-5 {
    width: 220px;
    height: 220px;
    bottom: 40%;
    right: 10%;
    animation-delay: -20s;
}

@keyframes lexai-float {
    0%, 100% {
        transform: translate(0, 0) scale(1);
    }
    25% {
        transform: translate(30px, -30px) scale(1.1);
    }
    50% {
        transform: translate(-20px, 20px) scale(0.9);
    }
    75% {
        transform: translate(20px, -10px) scale(1.05);
    }
}

/* ===== MAIN APP LAYOUT ===== */
.lexai-app {
    display: flex;
    height: 100vh;
    width: 100%;
}

/* ===== SIDEBAR ===== */
.lexai-sidebar {
    width: var(--lexai-sidebar-width);
    background: var(--lexai-bg-sidebar);
    backdrop-filter: var(--lexai-backdrop-blur);
    -webkit-backdrop-filter: var(--lexai-backdrop-blur);
    border-right: 1px solid var(--lexai-border);
    display: flex;
    flex-direction: column;
    transition: width var(--lexai-transition), transform var(--lexai-transition);
    z-index: var(--lexai-z-sidebar);
    position: relative;
}

.lexai-sidebar.collapsed {
    width: var(--lexai-sidebar-collapsed-width);
}

/* Ocultar texto cuando el sidebar está colapsado */
.lexai-sidebar.collapsed .lexai-logo span,
.lexai-sidebar.collapsed .lexai-new-chat-btn span,
.lexai-sidebar.collapsed .lexai-conversation-item span,
.lexai-sidebar.collapsed .lexai-search-box input,
.lexai-sidebar.collapsed .lexai-user-name,
.lexai-sidebar.collapsed .lexai-user-email {
    display: none;
}

/* Centrar iconos cuando está colapsado */
.lexai-sidebar.collapsed .lexai-logo,
.lexai-sidebar.collapsed .lexai-new-chat-btn,
.lexai-sidebar.collapsed .lexai-conversation-item {
    justify-content: center;
}

/* Ajustar padding cuando está colapsado */
.lexai-sidebar.collapsed .lexai-sidebar-header {
    padding: 1rem 0.5rem;
}

.lexai-sidebar.collapsed .lexai-new-chat-btn {
    padding: 0.75rem;
    min-width: auto;
}

/* Ocultar la caja de búsqueda cuando está colapsado */
.lexai-sidebar.collapsed .lexai-search-section {
    display: none;
}

.lexai-sidebar-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--lexai-border);
}

.lexai-logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 1.5rem;
    color: var(--lexai-text-sidebar);
    font-size: 1.25rem;
    font-weight: 700;
}

.lexai-logo i {
    font-size: 1.5rem;
    color: var(--lexai-accent);
}

.lexai-new-chat-btn {
    width: 100%;
    padding: 0.875rem 1rem;
    background: var(--lexai-gradient-primary);
    color: var(--lexai-text-tertiary);
    border: none;
    border-radius: var(--lexai-border-radius);
    font-weight: 600;
    cursor: pointer;
    transition: all var(--lexai-transition);
    display: flex;
    align-items: center;
    gap: 0.5rem;
    justify-content: center;
}

.lexai-new-chat-btn:hover {
    background: var(--lexai-gradient-secondary);
    transform: translateY(-2px);
    box-shadow: var(--lexai-shadow);
}

.lexai-sidebar-content {
    flex: 1;
    overflow-y: auto;
    padding: 1rem;
}

.lexai-search-section {
    margin-bottom: 1.5rem;
}

.lexai-search-box {
    position: relative;
    display: flex;
    align-items: center;
}

.lexai-search-box i {
    position: absolute;
    left: 0.875rem;
    color: var(--lexai-text-secondary);
    z-index: 1;
}

.lexai-search-box input {
    width: 100%;
    padding: 0.75rem 0.875rem 0.75rem 2.5rem;
    background: var(--lexai-bg-card);
    border: 1px solid var(--lexai-border);
    border-radius: var(--lexai-border-radius);
    color: var(--lexai-text-primary);
    font-size: 0.875rem;
    transition: all var(--lexai-transition-fast);
}

.lexai-search-box input:focus {
    outline: none;
    border-color: var(--lexai-accent);
    box-shadow: 0 0 0 3px rgba(var(--lexai-accent), 0.1);
}

.lexai-conversations-list {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.lexai-conversation-item {
    padding: 0.875rem;
    background: var(--lexai-bg-card);
    border: 1px solid var(--lexai-border);
    border-radius: var(--lexai-border-radius);
    cursor: pointer;
    transition: all var(--lexai-transition-fast);
    color: var(--lexai-text-primary);
}

.lexai-conversation-item:hover {
    background: var(--lexai-primary-light);
    border-color: var(--lexai-accent);
    transform: translateX(4px);
}

.lexai-conversation-item.active {
    background: var(--lexai-primary-light);
    border-color: var(--lexai-accent);
}

.lexai-sidebar-footer {
    padding: 1rem 1.5rem;
    border-top: 1px solid var(--lexai-border);
}

.lexai-user-profile {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    color: var(--lexai-text-sidebar);
}

.lexai-user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    overflow: hidden;
    flex-shrink: 0;
}

.lexai-user-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.lexai-user-info {
    flex: 1;
    min-width: 0;
}

.lexai-user-name {
    font-weight: 600;
    font-size: 0.875rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.lexai-user-role {
    font-size: 0.75rem;
    color: var(--lexai-text-secondary);
    text-transform: capitalize;
}

.lexai-user-menu-btn {
    background: none;
    border: none;
    color: var(--lexai-text-secondary);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: var(--lexai-border-radius);
    transition: all var(--lexai-transition-fast);
}

.lexai-user-menu-btn:hover {
    background: var(--lexai-primary-light);
    color: var(--lexai-text-primary);
}

/* ===== MAIN CONTENT ===== */
.lexai-main {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-width: 0;
}

/* ===== HEADER ===== */
.lexai-header {
    height: var(--lexai-header-height);
    background: var(--lexai-bg-card);
    backdrop-filter: var(--lexai-backdrop-blur);
    -webkit-backdrop-filter: var(--lexai-backdrop-blur);
    border-bottom: 1px solid var(--lexai-border);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 1.5rem;
    z-index: var(--lexai-z-header);
}

.lexai-header-left {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.lexai-sidebar-toggle {
    background: none;
    border: none;
    color: var(--lexai-text-primary);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: var(--lexai-border-radius);
    transition: all var(--lexai-transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
}

.lexai-sidebar-toggle:hover {
    background: var(--lexai-primary-light);
}

.lexai-conversation-title h1 {
    margin: 0;
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--lexai-text-primary);
}

.lexai-header-right {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.lexai-usage-indicator {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 0.25rem;
}

.lexai-usage-text {
    font-size: 0.75rem;
    color: var(--lexai-text-secondary);
    font-weight: 500;
}

.lexai-usage-bar {
    width: 80px;
    height: 4px;
    background: var(--lexai-border);
    border-radius: 2px;
    overflow: hidden;
}

.lexai-usage-fill {
    height: 100%;
    background: var(--lexai-gradient-primary);
    transition: width var(--lexai-transition);
}

.lexai-btn-glass {
    background: var(--lexai-bg-glass);
    backdrop-filter: var(--lexai-backdrop-blur);
    -webkit-backdrop-filter: var(--lexai-backdrop-blur);
    border: 1px solid var(--lexai-border);
    border-radius: var(--lexai-border-radius);
    padding: 0.75rem;
    color: var(--lexai-text-secondary);
    cursor: pointer;
    transition: all var(--lexai-transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 44px;
    height: 44px;
}

.lexai-btn-glass:hover {
    background: var(--lexai-primary-light);
    color: var(--lexai-text-primary);
    transform: translateY(-2px);
    box-shadow: var(--lexai-shadow);
}

.lexai-theme-toggle {
    background: var(--lexai-bg-glass);
    backdrop-filter: var(--lexai-backdrop-blur);
    -webkit-backdrop-filter: var(--lexai-backdrop-blur);
    border: 1px solid var(--lexai-border);
    border-radius: var(--lexai-border-radius);
    padding: 0.75rem;
    cursor: pointer;
    transition: all var(--lexai-transition-fast);
    position: relative;
    width: 44px;
    height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.lexai-theme-toggle:hover {
    background: var(--lexai-primary-light);
    transform: translateY(-2px);
    box-shadow: var(--lexai-shadow);
}

.lexai-theme-icon-light,
.lexai-theme-icon-dark {
    position: absolute;
    transition: all var(--lexai-transition);
    color: var(--lexai-text-secondary);
}

.lexai-theme-dark .lexai-theme-icon-light {
    opacity: 0;
    transform: rotate(180deg);
}

.lexai-theme-dark .lexai-theme-icon-dark {
    opacity: 1;
    transform: rotate(0deg);
}

body:not(.lexai-theme-dark) .lexai-theme-icon-light {
    opacity: 1;
    transform: rotate(0deg);
}

body:not(.lexai-theme-dark) .lexai-theme-icon-dark {
    opacity: 0;
    transform: rotate(-180deg);
}

.lexai-settings-btn {
    background: var(--lexai-bg-glass);
    backdrop-filter: var(--lexai-backdrop-blur);
    -webkit-backdrop-filter: var(--lexai-backdrop-blur);
    border: 1px solid var(--lexai-border);
    border-radius: var(--lexai-border-radius);
    padding: 0.75rem;
    color: var(--lexai-text-secondary);
    cursor: pointer;
    transition: all var(--lexai-transition-fast);
    width: 44px;
    height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.lexai-settings-btn:hover {
    background: var(--lexai-primary-light);
    color: var(--lexai-text-primary);
    transform: translateY(-2px);
    box-shadow: var(--lexai-shadow);
}

/* ===== CHAT CONTAINER ===== */
.lexai-chat-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0;
}

.lexai-messages-area {
    flex: 1;
    overflow-y: auto;
    padding: 1.5rem;
    display: flex;
    flex-direction: column;
}

.lexai-messages-container {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    max-width: 800px;
    margin: 0 auto;
    width: 100%;
}

/* ===== WELCOME SCREEN ===== */
.lexai-welcome-screen {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 100%;
    padding: 2rem;
}

.lexai-welcome-content {
    text-align: center;
    max-width: 600px;
}

.lexai-welcome-logo {
    font-size: 4rem;
    color: var(--lexai-accent);
    margin-bottom: 1.5rem;
}

.lexai-welcome-content h2 {
    font-size: 1.875rem;
    font-weight: 700;
    color: var(--lexai-text-primary);
    margin-bottom: 1rem;
}

.lexai-welcome-content p {
    font-size: 1.125rem;
    color: var(--lexai-text-secondary);
    margin-bottom: 2rem;
    line-height: 1.6;
}

.lexai-quick-actions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: 2rem;
}

.lexai-quick-action {
    background: var(--lexai-bg-glass);
    backdrop-filter: var(--lexai-backdrop-blur);
    -webkit-backdrop-filter: var(--lexai-backdrop-blur);
    border: 1px solid var(--lexai-border);
    border-radius: var(--lexai-border-radius);
    padding: 1.5rem;
    cursor: pointer;
    transition: all var(--lexai-transition);
    text-align: center;
    color: var(--lexai-text-primary);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.75rem;
}

.lexai-quick-action:hover {
    background: var(--lexai-primary-light);
    transform: translateY(-4px);
    box-shadow: var(--lexai-shadow);
    border-color: var(--lexai-accent);
}

.lexai-quick-action i {
    font-size: 1.5rem;
    color: var(--lexai-accent) !important;
}

.lexai-quick-action span {
    font-weight: 600;
    font-size: 0.875rem;
}

/* Asegurar que los iconos cambien en modo oscuro */
.lexai-theme-dark .lexai-quick-action i {
    color: var(--lexai-accent) !important;
}

/* ===== MESSAGES ===== */
.lexai-message {
    display: flex;
    gap: 0.75rem;
    margin-bottom: 1.5rem;
    animation: lexai-message-in 0.3s ease-out;
}

.lexai-message-user {
    flex-direction: row-reverse;
}

.lexai-message-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--lexai-gradient-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--lexai-text-tertiary);
    font-size: 0.875rem;
    flex-shrink: 0;
}

.lexai-message-user .lexai-message-avatar {
    background: var(--lexai-accent);
}

.lexai-message-content {
    flex: 1;
    max-width: 70%;
}

.lexai-message-text {
    background: var(--lexai-bg-glass);
    backdrop-filter: var(--lexai-backdrop-blur);
    -webkit-backdrop-filter: var(--lexai-backdrop-blur);
    border: 1px solid var(--lexai-border);
    border-radius: var(--lexai-border-radius-lg);
    padding: 1rem 1.25rem;
    color: var(--lexai-text-primary);
    line-height: 1.6;
    word-wrap: break-word;
}

.lexai-message-user .lexai-message-text {
    background: var(--lexai-accent);
    color: var(--lexai-text-tertiary);
    border-color: var(--lexai-accent);
}

.lexai-message-time {
    font-size: 0.75rem;
    color: var(--lexai-text-secondary);
    margin-top: 0.5rem;
    padding: 0 0.25rem;
}

.lexai-message-actions {
    display: flex;
    gap: 0.5rem;
    margin-top: 0.75rem;
    opacity: 0;
    transition: opacity var(--lexai-transition-fast);
}

.lexai-message:hover .lexai-message-actions {
    opacity: 1;
}

/* Partial messages styling */
.lexai-partial-message {
    border-left: 4px solid #007cba;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    margin-bottom: 8px;
    animation: slideInFromLeft 0.3s ease-out;
}

.lexai-plan-message {
    border-left-color: #28a745;
    background: linear-gradient(135deg, #f8fff9 0%, #e8f5e8 100%);
}

.lexai-task-start {
    border-left-color: #ffc107;
    background: linear-gradient(135deg, #fffef8 0%, #fef9e7 100%);
}

.lexai-agent-assigned {
    border-left-color: #17a2b8;
    background: linear-gradient(135deg, #f8fdff 0%, #e7f7fa 100%);
}

.lexai-task-result {
    border-left-color: #28a745;
    background: linear-gradient(135deg, #f8fff9 0%, #e8f5e8 100%);
}

.lexai-task-error {
    border-left-color: #dc3545;
    background: linear-gradient(135deg, #fff8f8 0%, #f8e7e7 100%);
}

.lexai-final-response {
    border-left-color: #6f42c1;
    background: linear-gradient(135deg, #faf8ff 0%, #f0e7ff 100%);
    font-weight: 500;
}

.lexai-message-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 4px;
    font-size: 0.85em;
    color: #666;
}

.lexai-message-icon {
    font-size: 1.1em;
}

@keyframes slideInFromLeft {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.lexai-message-action-btn {
    background: var(--lexai-bg-card);
    border: 1px solid var(--lexai-border);
    border-radius: var(--lexai-border-radius);
    padding: 0.375rem 0.75rem;
    font-size: 0.75rem;
    color: var(--lexai-text-secondary);
    cursor: pointer;
    transition: all var(--lexai-transition-fast);
    display: flex;
    align-items: center;
    gap: 0.375rem;
}

.lexai-message-action-btn:hover {
    background: var(--lexai-primary-light);
    color: var(--lexai-text-primary);
    border-color: var(--lexai-accent);
}

@keyframes lexai-message-in {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* ===== TYPING INDICATOR ===== */
.lexai-typing-indicator {
    display: flex;
    gap: 0.75rem;
    margin-bottom: 1.5rem;
    animation: lexai-message-in 0.3s ease-out;
}

.lexai-typing-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--lexai-gradient-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--lexai-text-tertiary);
    font-size: 0.875rem;
    flex-shrink: 0;
}

.lexai-typing-content {
    background: var(--lexai-bg-glass);
    backdrop-filter: var(--lexai-backdrop-blur);
    -webkit-backdrop-filter: var(--lexai-backdrop-blur);
    border: 1px solid var(--lexai-border);
    border-radius: var(--lexai-border-radius-lg);
    padding: 1rem 1.25rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.lexai-typing-dots {
    display: flex;
    gap: 0.25rem;
}

.lexai-typing-dots span {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: var(--lexai-accent);
    animation: lexai-typing-dot 1.4s ease-in-out infinite;
}

.lexai-typing-dots span:nth-child(2) {
    animation-delay: 0.2s;
}

.lexai-typing-dots span:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes lexai-typing-dot {
    0%, 60%, 100% {
        transform: scale(1);
        opacity: 0.5;
    }
    30% {
        transform: scale(1.2);
        opacity: 1;
    }
}

.lexai-typing-text {
    font-size: 0.875rem;
    color: var(--lexai-text-secondary);
}

/* ===== INPUT AREA ===== */
.lexai-input-area {
    padding: 1.5rem;
    background: var(--lexai-bg-card);
    backdrop-filter: var(--lexai-backdrop-blur);
    -webkit-backdrop-filter: var(--lexai-backdrop-blur);
    border-top: 1px solid var(--lexai-border);
}

.lexai-input-container {
    max-width: 800px;
    margin: 0 auto;
    width: 100%;
}

.lexai-input-wrapper {
    background: var(--lexai-bg-input);
    border: 1px solid var(--lexai-border);
    border-radius: var(--lexai-border-radius-lg);
    display: flex;
    align-items: flex-end;
    gap: 0.75rem;
    padding: 0.75rem;
    transition: all var(--lexai-transition-fast);
    position: relative;
}

.lexai-input-wrapper:focus-within {
    border-color: var(--lexai-accent);
    box-shadow: 0 0 0 3px rgba(var(--lexai-accent), 0.1);
}

.lexai-attach-btn,
.lexai-voice-btn {
    background: none;
    border: none;
    color: var(--lexai-text-secondary);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: var(--lexai-border-radius);
    transition: all var(--lexai-transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    flex-shrink: 0;
}

.lexai-attach-btn:hover,
.lexai-voice-btn:hover {
    background: var(--lexai-primary-light);
    color: var(--lexai-text-primary);
}

.lexai-voice-btn.recording {
    background: var(--lexai-accent);
    color: var(--lexai-text-tertiary);
    animation: lexai-pulse 1s ease-in-out infinite;
}

@keyframes lexai-pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
}

.lexai-message-input {
    flex: 1;
    border: none;
    outline: none;
    background: transparent;
    color: var(--lexai-text-primary);
    font-size: 1rem;
    line-height: 1.5;
    resize: none;
    min-height: 24px;
    max-height: 120px;
    padding: 0.5rem 0;
    font-family: inherit;
}

.lexai-message-input::placeholder {
    color: var(--lexai-text-secondary);
}

.lexai-send-btn {
    background: var(--lexai-gradient-primary);
    border: none;
    border-radius: var(--lexai-border-radius);
    color: var(--lexai-text-tertiary);
    cursor: pointer;
    padding: 0.5rem;
    transition: all var(--lexai-transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    flex-shrink: 0;
}

.lexai-send-btn:hover:not(:disabled) {
    background: var(--lexai-gradient-secondary);
    transform: translateY(-2px);
    box-shadow: var(--lexai-shadow);
}

.lexai-send-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

.lexai-input-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 0.75rem;
    font-size: 0.75rem;
    color: var(--lexai-text-secondary);
}

.lexai-char-counter {
    font-weight: 500;
}

.lexai-input-hint {
    text-align: center;
    flex: 1;
}

.lexai-powered-by {
    font-weight: 500;
}

/* ===== FILE PREVIEW ===== */
.lexai-file-preview-area {
    margin-bottom: 1rem;
    padding: 1rem;
    background: var(--lexai-bg-glass);
    backdrop-filter: var(--lexai-backdrop-blur);
    -webkit-backdrop-filter: var(--lexai-backdrop-blur);
    border: 1px solid var(--lexai-border);
    border-radius: var(--lexai-border-radius);
}

.lexai-file-previews {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
}

.lexai-file-preview {
    background: var(--lexai-bg-card);
    border: 1px solid var(--lexai-border);
    border-radius: var(--lexai-border-radius);
    padding: 0.75rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    position: relative;
    min-width: 200px;
}

.lexai-file-preview i {
    color: var(--lexai-accent);
    font-size: 1.125rem;
}

.lexai-file-info {
    flex: 1;
    min-width: 0;
}

.lexai-file-name {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--lexai-text-primary);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.lexai-file-size {
    font-size: 0.75rem;
    color: var(--lexai-text-secondary);
}

.lexai-file-remove {
    background: none;
    border: none;
    color: var(--lexai-text-secondary);
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 50%;
    transition: all var(--lexai-transition-fast);
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.lexai-file-remove:hover {
    background: var(--lexai-accent);
    color: var(--lexai-text-tertiary);
}

/* ===== MODALS ===== */
.lexai-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
    -webkit-backdrop-filter: blur(4px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: var(--lexai-z-modal);
    opacity: 0;
    visibility: hidden;
    transition: all var(--lexai-transition);
}

.lexai-modal.show {
    opacity: 1;
    visibility: visible;
}

.lexai-modal-content {
    background: var(--lexai-bg-card);
    backdrop-filter: var(--lexai-backdrop-blur);
    -webkit-backdrop-filter: var(--lexai-backdrop-blur);
    border: 1px solid var(--lexai-border);
    border-radius: var(--lexai-border-radius-lg);
    box-shadow: var(--lexai-shadow);
    max-width: 90vw;
    max-height: 90vh;
    overflow-y: auto;
    transform: translateY(20px);
    transition: transform var(--lexai-transition);
}

.lexai-modal.show .lexai-modal-content {
    transform: translateY(0);
}

.lexai-modal-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--lexai-border);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.lexai-modal-title {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--lexai-text-primary);
}

.lexai-modal-close {
    background: none;
    border: none;
    color: var(--lexai-text-secondary);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: var(--lexai-border-radius);
    transition: all var(--lexai-transition-fast);
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.lexai-modal-close:hover {
    background: var(--lexai-primary-light);
    color: var(--lexai-text-primary);
}

.lexai-modal-body {
    padding: 1.5rem;
}

.lexai-modal-footer {
    padding: 1.5rem;
    border-top: 1px solid var(--lexai-border);
    display: flex;
    gap: 0.75rem;
    justify-content: flex-end;
}

/* ===== BUTTONS ===== */
.lexai-btn {
    padding: 0.75rem 1.5rem;
    border-radius: var(--lexai-border-radius);
    font-weight: 600;
    cursor: pointer;
    transition: all var(--lexai-transition-fast);
    border: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
    font-size: 0.875rem;
}

.lexai-btn-primary {
    background: var(--lexai-gradient-primary);
    color: var(--lexai-text-tertiary);
}

.lexai-btn-primary:hover {
    background: var(--lexai-gradient-secondary);
    transform: translateY(-2px);
    box-shadow: var(--lexai-shadow);
}

.lexai-btn-secondary {
    background: var(--lexai-bg-card);
    color: var(--lexai-text-primary);
    border: 1px solid var(--lexai-border);
}

.lexai-btn-secondary:hover {
    background: var(--lexai-primary-light);
    border-color: var(--lexai-accent);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
    .lexai-sidebar {
        position: fixed;
        top: 0;
        left: 0;
        height: 100vh;
        transform: translateX(-100%);
        z-index: var(--lexai-z-overlay);
    }
    
    .lexai-sidebar.open {
        transform: translateX(0);
    }
    
    .lexai-sidebar-toggle {
        display: flex;
    }
    
    .lexai-main {
        width: 100%;
    }
    
    .lexai-header {
        padding: 0 1rem;
    }
    
    .lexai-messages-area {
        padding: 1rem;
    }
    
    .lexai-input-area {
        padding: 1rem;
    }
    
    .lexai-message-content {
        max-width: 85%;
    }
    
    .lexai-quick-actions {
        grid-template-columns: 1fr;
    }
    
    .lexai-modal-content {
        margin: 1rem;
        max-width: none;
    }
    
    .lexai-modal-header,
    .lexai-modal-body,
    .lexai-modal-footer {
        padding: 1rem;
    }
    
    .lexai-header-right {
        gap: 0.5rem;
    }
    
    .lexai-usage-indicator {
        display: none;
    }
}

/* ===== SIDEBAR OVERLAY ===== */
.lexai-sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(2px);
    -webkit-backdrop-filter: blur(2px);
    z-index: calc(var(--lexai-z-sidebar) - 1);
    opacity: 0;
    visibility: hidden;
    transition: all var(--lexai-transition);
}

.lexai-sidebar-overlay.show {
    opacity: 1;
    visibility: visible;
}

/* ===== TOAST NOTIFICATIONS ===== */
.lexai-toast-container {
    position: fixed;
    top: 1rem;
    right: 1rem;
    z-index: var(--lexai-z-toast);
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.lexai-toast {
    background: var(--lexai-bg-card);
    backdrop-filter: var(--lexai-backdrop-blur);
    -webkit-backdrop-filter: var(--lexai-backdrop-blur);
    border: 1px solid var(--lexai-border);
    border-radius: var(--lexai-border-radius);
    padding: 1rem;
    box-shadow: var(--lexai-shadow);
    min-width: 300px;
    transform: translateX(100%);
    transition: transform var(--lexai-transition);
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.lexai-toast.show {
    transform: translateX(0);
}

.lexai-toast-icon {
    font-size: 1.25rem;
    flex-shrink: 0;
}

.lexai-toast-success .lexai-toast-icon {
    color: var(--lexai-success);
}

.lexai-toast-error .lexai-toast-icon {
    color: var(--lexai-error);
}

.lexai-toast-warning .lexai-toast-icon {
    color: var(--lexai-warning);
}

.lexai-toast-info .lexai-toast-icon {
    color: var(--lexai-info);
}

.lexai-toast-content {
    flex: 1;
}

.lexai-toast-title {
    font-weight: 600;
    color: var(--lexai-text-primary);
    margin-bottom: 0.25rem;
}

.lexai-toast-message {
    font-size: 0.875rem;
    color: var(--lexai-text-secondary);
}

.lexai-toast-close {
    background: none;
    border: none;
    color: var(--lexai-text-secondary);
    cursor: pointer;
    padding: 0.25rem;
    border-radius: var(--lexai-border-radius);
    transition: all var(--lexai-transition-fast);
}

.lexai-toast-close:hover {
    background: var(--lexai-primary-light);
    color: var(--lexai-text-primary);
}

/* ===== SCROLLBAR STYLING ===== */
.lexai-messages-area::-webkit-scrollbar,
.lexai-sidebar-content::-webkit-scrollbar {
    width: 6px;
}

.lexai-messages-area::-webkit-scrollbar-track,
.lexai-sidebar-content::-webkit-scrollbar-track {
    background: transparent;
}

.lexai-messages-area::-webkit-scrollbar-thumb,
.lexai-sidebar-content::-webkit-scrollbar-thumb {
    background: var(--lexai-border);
    border-radius: 3px;
}

.lexai-messages-area::-webkit-scrollbar-thumb:hover,
.lexai-sidebar-content::-webkit-scrollbar-thumb:hover {
    background: var(--lexai-text-secondary);
}

/* ===== ACCESSIBILITY ===== */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Focus styles for keyboard navigation */
.lexai-btn:focus,
.lexai-message-input:focus,
.lexai-search-box input:focus,
button:focus {
    outline: 2px solid var(--lexai-accent);
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    :root {
        --lexai-border: #000000;
        --lexai-text-secondary: #000000;
    }
    
    .lexai-theme-dark {
        --lexai-border: #ffffff;
        --lexai-text-secondary: #ffffff;
    }
}

/* ===== UTILITY CLASSES ===== */
.lexai-hidden {
    display: none !important;
}

.lexai-sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

.lexai-loading {
    opacity: 0.6;
    pointer-events: none;
}

.lexai-disabled {
    opacity: 0.5;
    pointer-events: none;
}

/* ===== PRINT STYLES ===== */
@media print {
    .lexai-sidebar,
    .lexai-header,
    .lexai-input-area,
    .lexai-modal,
    .lexai-toast-container {
        display: none !important;
    }
    
    .lexai-main {
        width: 100% !important;
    }
    
    .lexai-messages-area {
        overflow: visible !important;
        height: auto !important;
    }
    
    .lexai-message {
        break-inside: avoid;
    }
}

/* ===== TYPING INDICATOR ANIMATION ===== */
.lexai-typing-animation {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 15px 20px;
    background: var(--lexai-bg-bubble);
    border-radius: 18px;
    border-top-left-radius: 4px;
}

.lexai-typing-dots {
    display: flex;
    gap: 4px;
}

.lexai-typing-dots span {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--lexai-primary);
    animation: lexai-typing-bounce 1.4s infinite ease-in-out;
}

.lexai-typing-dots span:nth-child(1) {
    animation-delay: -0.32s;
}

.lexai-typing-dots span:nth-child(2) {
    animation-delay: -0.16s;
}

.lexai-typing-dots span:nth-child(3) {
    animation-delay: 0s;
}

.lexai-typing-text {
    font-size: 14px;
    color: var(--lexai-text-secondary);
    font-style: italic;
}

@keyframes lexai-typing-bounce {
    0%, 80%, 100% {
        transform: scale(0.8);
        opacity: 0.5;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}