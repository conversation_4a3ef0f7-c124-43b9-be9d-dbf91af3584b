// Debug authentication in browser console
console.log('=== LexAI Authentication Debug ===');

// Check if lexaiConfig exists
if (typeof lexaiConfig !== 'undefined') {
    console.log('✅ lexaiConfig found:', lexaiConfig);
    console.log('User ID:', lexaiConfig.userId);
    console.log('Nonce:', lexaiConfig.nonce);
    console.log('AJAX URL:', lexaiConfig.ajaxUrl);
} else {
    console.log('❌ lexaiConfig not found');
}

// Test AJAX endpoint
function testAuth() {
    console.log('Testing authentication...');
    
    $.ajax({
        url: '/wp-admin/admin-ajax.php',
        type: 'POST',
        data: {
            action: 'lexai_send_message_fullpage',
            message: 'Test auth',
            nonce: lexaiConfig ? lexaiConfig.nonce : 'test'
        },
        success: function(response) {
            console.log('✅ AJAX Success:', response);
        },
        error: function(xhr, status, error) {
            console.log('❌ AJAX Error:', {
                status: status,
                error: error,
                responseText: xhr.responseText,
                statusCode: xhr.status
            });
        }
    });
}

// Run test
testAuth();

// Also check WordPress user info
if (typeof wp !== 'undefined' && wp.ajax) {
    console.log('✅ WordPress AJAX available');
} else {
    console.log('❌ WordPress AJAX not available');
}

console.log('=== End Debug ===');
