# Análisis Profundo del Sistema de Orquestación LexAI

## Resumen Ejecutivo

El sistema posee una base de backend robusta con una lógica de orquestación clara y una persistencia de datos bien estructurada. Sin embargo, sufre de un problema arquitectónico crítico: **toda la comunicación es síncrona**. Esto provoca una experiencia de usuario deficiente (sin feedback en tiempo real), riesgos de rendimiento (timeouts) y una falta de resiliencia ante fallos. La solución principal no es arreglar pequeños bugs, sino **rediseñar el flujo de comunicación para que sea asíncrono**, utilizando la persistencia de datos ya existente como un mecanismo de estado compartido.

---

## 1. Flujo de Interacción del Usuario (Frontend)

-   **Archivos Clave:** `public/js/lexai-fullpage.js`, `templates/fullpage-chat-template.php`

### Diagnóstico

-   **Lógica:** El JavaScript envía una única petición AJAX y espera una única respuesta final. No está diseñado para manejar actualizaciones intermedias.
-   **Rendimiento:** La interfaz de usuario se bloquea visualmente, mostrando solo un indicador de "escribiendo" durante todo el proceso del backend, que puede durar desde segundos hasta más de un minuto. Esto es inaceptable desde la perspectiva de la UX.
-   **Funcionalidad Incompleta:**
    -   El HTML carece de elementos específicos para mostrar un plan de tareas, el estado de cada tarea o los "pensamientos" del agente.
    -   El JavaScript no tiene lógica para sondear (poll) al servidor en busca de actualizaciones de estado.
-   **Sinergia:** La simplicidad del frontend está en conflicto directo con la complejidad y la naturaleza multipaso del proceso del backend.

### Recomendaciones

-   Implementar un **mecanismo de sondeo (polling)** en `lexai-fullpage.js`.
-   Enriquecer el `fullpage-chat-template.php` con contenedores HTML para mostrar el plan y el estado de las tareas.

---

## 2. Comunicación Frontend-Backend (AJAX)

-   **Archivo Clave:** `includes/class-lexai-ajax.php`

### Diagnóstico

-   **Lógica (Crítico):** El endpoint `lexai_send_message` es **bloqueante**. Llama al orquestador y espera a que termine, lo que constituye el núcleo del problema.
-   **Funcionalidad Incompleta:** No existe un endpoint para que el frontend consulte el estado de una conversación/proceso en curso. Sin esto, el sondeo es imposible.
-   **Rendimiento:** Mantener una conexión PHP abierta durante mucho tiempo es ineficiente y propenso a timeouts.

### Recomendaciones

-   **Modificar `handle_send_message`:** Debe iniciar el proceso de orquestación de forma asíncrona (p. ej., usando `wp_schedule_single_event` o una solución de background jobs) y devolver inmediatamente un `conversation_id` o `process_id`.
-   **Crear un Nuevo Endpoint (`get_lexai_status`):** Este endpoint recibirá un `process_id`, consultará las tablas `lexai_tasks` y `lexai_task_executions`, y devolverá el estado actual del plan (tareas pendientes, en proceso, completadas y sus resultados).

---

## 3. Procesamiento del Backend (Orquestación)

-   **Archivo Clave:** `includes/class-lexai-orchestrator.php`

### Diagnóstico

-   **Lógica:** El método `run()` es un proceso monolítico y secuencial. Aunque utiliza la base de datos, lo hace como un registro, no como un sistema de estado en vivo.
-   **Fortaleza (No Explotada):** La persistencia del estado del plan en la base de datos (`lexai_tasks`) es la clave para la solución. El sistema ya sabe qué tarea ejecutar a continuación.
-   **Rendimiento:** Las múltiples llamadas secuenciales a la API de Gemini dentro de una sola ejecución de script son un cuello de botella importante.
-   **Sinergia:** El orquestador no está diseñado para ser interrumpido y reanudado, aunque la lógica de la base de datos lo permitiría.

### Recomendaciones

-   **Refactorizar el Orquestador:** Dividir el método `run()` en partes más pequeñas que puedan ser ejecutadas por un procesador de tareas en segundo plano. El orquestador no debería "ejecutar" el plan completo, sino más bien "avanzar un paso" en el plan.

---

## 4. Ejecución de Agentes y Herramientas

-   **Archivos Clave:** `includes/class-lexai-agent-instance.php`, `includes/mcp/class-lexai-tool-executor.php`

### Diagnóstico

-   **Lógica:** La ejecución de agentes y herramientas también es síncrona y bloqueante.
-   **Rendimiento:** Una herramienta lenta (ej. `Web_Scraper`) puede detener todo el proceso.
-   **Funcionalidad Incompleta:** No hay un registro granular del progreso de una herramienta. El estado es simplemente "ejecutando" o "terminado".

### Recomendaciones

-   Para tareas de herramientas muy largas (como scraping complejo), considerar ejecutarlas también como trabajos en segundo plano separados, actualizando su estado en la tabla `lexai_task_executions`.

---

## 5. Persistencia de Datos (Base de Datos)

-   **Archivo Clave:** `includes/class-lexai-db.php`

### Diagnóstico

-   **Fortaleza:** La estructura de las tablas (`lexai_tasks`, `lexai_task_executions`) es excelente y perfectamente adecuada para soportar un flujo asíncrono. Almacena toda la información necesaria: el plan, el estado de cada tarea y los resultados.
-   **Debilidad:** Su potencial está completamente infrautilizado, sirviendo actualmente como un simple log en lugar de la columna vertebral de la comunicación de estado.

### Recomendaciones

-   Utilizar estas tablas como la **fuente de verdad** para el nuevo endpoint de estado (`get_lexai_status`).

---

## 6. Manejo de Errores

### Diagnóstico

-   **Lógica:** Un fallo en cualquier punto de la cadena de ejecución síncrona provoca un error genérico en el frontend. El usuario no sabe qué falló ni por qué.
-   **Funcionalidad Incompleta:** La base de datos registra el estado de fallo, pero esta información no se comunica de forma útil al usuario.

### Recomendaciones

-   El nuevo endpoint de estado (`get_lexai_status`) debe ser capaz de leer el estado de "fallo" de una tarea desde la base de datos y comunicarlo al frontend.
-   El frontend debe ser capaz de mostrar qué tarea específica falló y, si es posible, el motivo del fallo.
