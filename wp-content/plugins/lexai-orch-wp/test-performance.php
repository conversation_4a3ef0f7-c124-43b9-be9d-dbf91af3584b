<?php
/**
 * Performance diagnostic script
 */

// Start timing
$start_time = microtime(true);

// WordPress environment
require_once('../../../wp-config.php');

echo "<h1>LexAI Performance Diagnostic</h1>";

// Test 1: Basic WordPress load time
$wp_load_time = microtime(true) - $start_time;
echo "<h2>WordPress Load Time: " . round($wp_load_time * 1000, 2) . "ms</h2>";

// Test 2: Plugin initialization
$plugin_start = microtime(true);
$lexai = LexAI::get_instance();
$plugin_init_time = microtime(true) - $plugin_start;
echo "<h2>Plugin Initialization: " . round($plugin_init_time * 1000, 2) . "ms</h2>";

// Test 3: Database connection
$db_start = microtime(true);
$db_test = $lexai->db->get_agents('active');
$db_time = microtime(true) - $db_start;
echo "<h2>Database Query (get agents): " . round($db_time * 1000, 2) . "ms</h2>";
echo "<p>Found " . count($db_test) . " active agents</p>";

// Test 4: Usage limiter initialization
$usage_start = microtime(true);
if (!$lexai->usage_limiter) {
    $lexai->usage_limiter = new LexAI_Usage_Limiter($lexai->db);
}
$user_id = get_current_user_id();
if ($user_id) {
    $usage_stats = $lexai->usage_limiter->get_usage_stats($user_id);
    $usage_time = microtime(true) - $usage_start;
    echo "<h2>Usage Stats Query: " . round($usage_time * 1000, 2) . "ms</h2>";
    echo "<p>Usage: " . json_encode($usage_stats) . "</p>";
} else {
    echo "<h2>No user logged in - skipping usage stats</h2>";
}

// Test 5: Memory usage
$memory_usage = memory_get_usage(true);
$memory_peak = memory_get_peak_usage(true);
echo "<h2>Memory Usage</h2>";
echo "<p>Current: " . round($memory_usage / 1024 / 1024, 2) . " MB</p>";
echo "<p>Peak: " . round($memory_peak / 1024 / 1024, 2) . " MB</p>";

// Test 6: File system checks
echo "<h2>File System Checks</h2>";
$plugin_dir = WP_PLUGIN_DIR . '/lexai-orch-wp/';
$css_file = $plugin_dir . 'assets/css/lexai-fullpage-chat.css';
$js_file = $plugin_dir . 'assets/js/lexai-fullpage-chat.js';

echo "<p>CSS file exists: " . (file_exists($css_file) ? '✅ Yes' : '❌ No') . "</p>";
echo "<p>JS file exists: " . (file_exists($js_file) ? '✅ Yes' : '❌ No') . "</p>";

if (file_exists($css_file)) {
    echo "<p>CSS file size: " . round(filesize($css_file) / 1024, 2) . " KB</p>";
}
if (file_exists($js_file)) {
    echo "<p>JS file size: " . round(filesize($js_file) / 1024, 2) . " KB</p>";
}

// Test 7: External dependencies
echo "<h2>External Dependencies</h2>";
$fontawesome_start = microtime(true);
$fontawesome_response = wp_remote_head('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css');
$fontawesome_time = microtime(true) - $fontawesome_start;

if (is_wp_error($fontawesome_response)) {
    echo "<p>FontAwesome CDN: ❌ Error - " . $fontawesome_response->get_error_message() . "</p>";
} else {
    echo "<p>FontAwesome CDN: ✅ OK (" . round($fontawesome_time * 1000, 2) . "ms)</p>";
}

// Test 8: WordPress hooks and actions
echo "<h2>WordPress Hooks</h2>";
global $wp_filter;
$lexai_hooks = 0;
foreach ($wp_filter as $hook_name => $hook) {
    if (strpos($hook_name, 'lexai') !== false) {
        $lexai_hooks++;
    }
}
echo "<p>LexAI hooks registered: " . $lexai_hooks . "</p>";

// Total time
$total_time = microtime(true) - $start_time;
echo "<h2>Total Execution Time: " . round($total_time * 1000, 2) . "ms</h2>";

// Recommendations
echo "<h2>Performance Recommendations</h2>";
if ($wp_load_time > 0.5) {
    echo "<p>⚠️ WordPress load time is slow (>" . round($wp_load_time * 1000) . "ms). Consider optimizing your hosting or other plugins.</p>";
}
if ($plugin_init_time > 0.1) {
    echo "<p>⚠️ Plugin initialization is slow (>" . round($plugin_init_time * 1000) . "ms). Consider lazy loading components.</p>";
}
if ($db_time > 0.05) {
    echo "<p>⚠️ Database queries are slow (>" . round($db_time * 1000) . "ms). Consider database optimization.</p>";
}
if ($memory_peak > 128 * 1024 * 1024) {
    echo "<p>⚠️ High memory usage (" . round($memory_peak / 1024 / 1024) . "MB). Consider optimizing memory usage.</p>";
}

echo "<h3>Optimization Tips:</h3>";
echo "<ul>";
echo "<li>✅ Implemented: Async loading of user stats</li>";
echo "<li>✅ Implemented: Async loading of FontAwesome</li>";
echo "<li>✅ Implemented: User data caching</li>";
echo "<li>🔄 Consider: Database query caching</li>";
echo "<li>🔄 Consider: Lazy loading of heavy components</li>";
echo "<li>🔄 Consider: CDN for static assets</li>";
echo "</ul>";
?>
