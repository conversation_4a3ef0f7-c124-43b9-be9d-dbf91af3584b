<?php
/**
 * Direct test without WordPress security
 */

// Load WordPress
require_once('../../../wp-config.php');

echo "<h1>Direct Test</h1>";

// Test if user is logged in
$user_id = get_current_user_id();
echo "<p>Current User ID: " . ($user_id ? $user_id : 'Not logged in') . "</p>";

if (!$user_id) {
    echo "<p style='color: red;'>❌ User not logged in - this is the problem!</p>";
    echo "<p>The AJAX requests are failing because the user is not authenticated.</p>";
    
    // Try to log in programmatically for testing
    $user = get_user_by('login', 'admin'); // Change to your admin username
    if ($user) {
        wp_set_current_user($user->ID);
        wp_set_auth_cookie($user->ID);
        echo "<p style='color: green;'>✅ Logged in as: " . $user->user_login . "</p>";
        echo "<p><a href='/lexai-chat/'>Try the chat now</a></p>";
    } else {
        echo "<p style='color: red;'>❌ Could not find admin user</p>";
    }
} else {
    echo "<p style='color: green;'>✅ User is logged in</p>";
    
    // Test the AJAX endpoint directly
    echo "<h2>Testing AJAX Endpoint</h2>";
    
    // Create nonce
    $nonce = wp_create_nonce('lexai_fullpage_nonce');
    echo "<p>Nonce: $nonce</p>";
    
    // Test data
    $test_data = array(
        'action' => 'lexai_send_message_fullpage',
        'message' => 'Test message',
        'nonce' => $nonce
    );
    
    echo "<p>Test data: " . json_encode($test_data) . "</p>";
    
    // Simulate AJAX request
    $_POST = $test_data;
    
    try {
        // Get the LexAI instance
        $lexai = LexAI::get_instance();
        if ($lexai && $lexai->public_fullpage) {
            echo "<p style='color: green;'>✅ LexAI instance found</p>";
            
            // Try to call the handler directly
            ob_start();
            $lexai->public_fullpage->handle_send_message();
            $output = ob_get_clean();
            
            echo "<h3>Handler Output:</h3>";
            echo "<pre>" . htmlspecialchars($output) . "</pre>";
            
        } else {
            echo "<p style='color: red;'>❌ LexAI instance not found</p>";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
    }
}

echo "<h2>Security Status</h2>";
echo "<p>Recent security locks in debug.log indicate there may be authentication issues.</p>";
echo "<p>Check if the user session is valid and the nonce is being generated correctly.</p>";
?>
