<?php
/**
 * Simple WordPress Test
 */

$start_time = microtime(true);

echo "<h1>Simple WordPress Performance Test</h1>";

// Load WordPress
require_once('../../../wp-config.php');

$wp_load_time = microtime(true) - $start_time;
echo "<p>✅ WordPress loaded in: " . round($wp_load_time * 1000, 2) . "ms</p>";

// Test basic WordPress functions
$wp_test_start = microtime(true);
$user_id = get_current_user_id();
$site_url = get_site_url();
$wp_test_time = microtime(true) - $wp_test_start;
echo "<p>✅ WordPress functions: " . round($wp_test_time * 1000, 2) . "ms</p>";

// Test if LexAI class exists
if (class_exists('LexAI')) {
    echo "<p>✅ LexAI class exists</p>";
    
    // Test instance creation (carefully)
    $instance_start = microtime(true);
    try {
        $lexai = LexAI::get_instance();
        $instance_time = microtime(true) - $instance_start;
        echo "<p>✅ LexAI instance created: " . round($instance_time * 1000, 2) . "ms</p>";
        
        // Test database connection
        if (isset($lexai->db)) {
            echo "<p>✅ Database object exists</p>";
        } else {
            echo "<p>⚠️ Database object missing</p>";
        }
        
    } catch (Exception $e) {
        $instance_time = microtime(true) - $instance_start;
        echo "<p>❌ LexAI instance failed (" . round($instance_time * 1000, 2) . "ms): " . $e->getMessage() . "</p>";
    }
} else {
    echo "<p>❌ LexAI class not found</p>";
}

$total_time = microtime(true) - $start_time;
echo "<h2>Total time: " . round($total_time * 1000, 2) . "ms</h2>";

if ($total_time < 0.5) {
    echo "<p style='color: green;'>✅ Performance is good</p>";
} else {
    echo "<p style='color: orange;'>⚠️ Performance could be better</p>";
}
?>
